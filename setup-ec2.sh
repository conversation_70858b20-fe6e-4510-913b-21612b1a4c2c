#!/bin/bash

# EC2 Initial Setup Script for MCP Client
# Run this script immediately after launching a new EC2 instance

set -e

echo "Setting up EC2 instance for MCP Client deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Update system
log_info "Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install essential packages
log_info "Installing essential packages..."
sudo apt install -y \
    curl \
    wget \
    git \
    unzip \
    htop \
    tree \
    vim \
    ufw \
    fail2ban \
    python3 \
    python3-pip \
    python3-venv \
    nginx \
    certbot \
    python3-certbot-nginx

# Configure firewall
log_info "Configuring UFW firewall..."
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw --force enable

# Configure fail2ban
log_info "Configuring fail2ban..."
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Create swap file (recommended for small instances)
log_info "Creating swap file..."
if [ ! -f /swapfile ]; then
    sudo fallocate -l 2G /swapfile
    sudo chmod 600 /swapfile
    sudo mkswap /swapfile
    sudo swapon /swapfile
    echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
fi

# Configure automatic security updates
log_info "Configuring automatic security updates..."
sudo apt install -y unattended-upgrades
echo 'Unattended-Upgrade::Automatic-Reboot "false";' | sudo tee -a /etc/apt/apt.conf.d/50unattended-upgrades

# Set timezone
log_info "Setting timezone to UTC..."
sudo timedatectl set-timezone UTC

# Configure log rotation
log_info "Setting up log rotation..."
sudo tee /etc/logrotate.d/mcp-client > /dev/null <<EOF
/var/log/mcp-client/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 mcpuser mcpuser
    postrotate
        systemctl reload mcp-client
    endscript
}
EOF

log_info "EC2 setup completed successfully!"
log_info "Next steps:"
log_info "1. Upload your application files to the server"
log_info "2. Run the deploy.sh script"
log_info "3. Configure your .env file with AWS credentials"
log_info "4. Start the service with: sudo systemctl start mcp-client"
