# Production Environment Configuration
# Copy this file to .env and update with your actual values

# AWS Configuration - REPLACE WITH YOUR ACTUAL VALUES
AWS_ACCESS_KEY_ID=AKIA...
AWS_SECRET_ACCESS_KEY=...
AWS_REGION=ap-south-1

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# Application Configuration
APP_ENV=production
LOG_LEVEL=INFO
HOST=127.0.0.1
PORT=8000

# Security Configuration - GENERATE A STRONG SECRET KEY
SECRET_KEY=your-super-secret-key-here-use-openssl-rand-hex-32

# CORS Configuration - UPDATE WITH YOUR ACTUAL DOMAIN
ALLOWED_ORIGINS=https://yourdomain.com

# Monitoring and Logging
ENABLE_METRICS=true
LOG_FILE_PATH=/var/log/mcp-client/app.log

# MCP Server Configuration Path
MCP_CONFIG_PATH=/opt/mcp-client/server_config.json

# Performance Settings
WORKERS=4
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100
TIMEOUT=30
