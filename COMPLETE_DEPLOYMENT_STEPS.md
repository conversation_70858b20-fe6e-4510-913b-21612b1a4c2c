# Complete MCP Client Deployment Guide

This guide provides step-by-step commands to deploy your MCP Client FastAPI application on Amazon Linux EC2 with IAM role authentication.

## Prerequisites

- EC2 instance running Amazon Linux 2 or 2023
- IAM role attached to EC2 with Bedrock permissions
- Security Group allowing SSH (22) and port 8000
- SSH key pair for instance access

## Step 1: Connect to Your EC2 Instance

```bash
# Connect to your Amazon Linux instance
ssh -i your-key.pem ec2-user@your-instance-public-ip
```

## Step 2: Update System and Install Python 3.10+

```bash
# Update system packages
sudo yum update -y

# Install essential tools
sudo yum install -y gcc git curl wget htop

# Check current Python version
python3 --version

# For Amazon Linux 2023 (has Python 3.9+ by default)
if grep -q "Amazon Linux 2023" /etc/os-release; then
    # Install Python 3.11 (available in AL2023)
    sudo yum install -y python3.11 python3.11-pip python3.11-devel
    # Create symlinks for easier use
    sudo alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
    sudo alternatives --install /usr/bin/pip3 pip3 /usr/bin/pip3.11 1
else
    # For Amazon Linux 2 - install Python 3.10 from source
    echo "Installing Python 3.10 from source (this may take 10-15 minutes)..."

    # Install build dependencies
    sudo yum groupinstall -y "Development Tools"
    sudo yum install -y openssl-devel bzip2-devel libffi-devel zlib-devel

    # Download and compile Python 3.10
    cd /tmp
    wget https://www.python.org/ftp/python/3.10.12/Python-3.10.12.tgz
    tar xzf Python-3.10.12.tgz
    cd Python-3.10.12
    ./configure --enable-optimizations --with-ensurepip=install
    make -j $(nproc)
    sudo make altinstall

    # Create symlinks
    sudo ln -sf /usr/local/bin/python3.10 /usr/bin/python3
    sudo ln -sf /usr/local/bin/pip3.10 /usr/bin/pip3

    # Clean up
    cd /
    rm -rf /tmp/Python-3.10.12*
fi

# Verify Python version (should be 3.10 or higher)
python3 --version
pip3 --version
```

## Step 3: Create Application Directories

```bash
# Create application directory
sudo mkdir -p /opt/mcp-client

# Create log directory
sudo mkdir -p /var/log/mcp-client

# Set permissions
sudo chmod 755 /opt/mcp-client
sudo chmod 755 /var/log/mcp-client
```

## Step 4: Create Application Files

### Create main.py

```bash
# Create main.py file
sudo nano /opt/mcp-client/main.py
```

**Copy and paste your entire `main.py` content** (the version with IAM role support), then save with `Ctrl+X`, `Y`, `Enter`

### Create server_config.json

```bash
# Create server_config.json file
sudo nano /opt/mcp-client/server_config.json
```

**Copy and paste this content:**

```json
{
  "mcpServers": {
    "cost-explorer": {
      "command": "uv",
      "args": [
        "tool",
        "run",
        "--from",
        "awslabs-cost-explorer-mcp-server@latest",
        "awslabs-cost-explorer-mcp-server"
      ],
      "env": {
        "AWS_PROFILE": "default",
        "AWS_REGION": "ap-south-1"
      }
    },
    "cloudformation": {
      "command": "uv",
      "args": [
        "tool",
        "run",
        "--from",
        "awslabs-cfn-mcp-server@latest",
        "awslabs-cfn-mcp-server"
      ],
      "env": {
        "AWS_PROFILE": "default",
        "AWS_REGION": "ap-south-1"
      }
    },
    "pricing": {
      "command": "uv",
      "args": [
        "tool",
        "run",
        "--from",
        "awslabs-aws-pricing-mcp-server@latest",
        "awslabs-aws-pricing-mcp-server"
      ],
      "env": {
        "AWS_PROFILE": "default",
        "AWS_REGION": "ap-south-1"
      }
    }
  }
}
```

Save with `Ctrl+X`, `Y`, `Enter`

### Create requirements.txt

```bash
# Create requirements.txt file
sudo nano /opt/mcp-client/requirements.txt
```

**Copy and paste this content:**

```
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
boto3==1.34.0
botocore==1.34.0
mcp==1.0.0
python-dotenv==1.0.0
asyncio-mqtt==0.16.1
httpx==0.25.2
aiofiles==23.2.0
python-multipart==0.0.6
gunicorn==21.2.0
structlog==23.2.0
```

Save with `Ctrl+X`, `Y`, `Enter`

## Step 5: Setup Python Virtual Environment

```bash
# Verify Python version is 3.10+
python3 --version

# Create virtual environment with Python 3.10+
sudo python3 -m venv /opt/mcp-client/venv

# Upgrade pip to latest version
sudo /opt/mcp-client/venv/bin/pip install --upgrade pip

# Install wheel for better package compilation
sudo /opt/mcp-client/venv/bin/pip install wheel

# Install dependencies (this may take 5-10 minutes)
sudo /opt/mcp-client/venv/bin/pip install -r /opt/mcp-client/requirements.txt

# Verify MCP installation
sudo /opt/mcp-client/venv/bin/python -c "import mcp; print(f'MCP version: {mcp.__version__}')"
```

## Step 6: Create Environment File (Optional)

```bash
# Create minimal environment file for IAM role setup
sudo nano /opt/mcp-client/.env
```

**Copy and paste this content:**

```bash
# Minimal Environment Configuration for IAM Role Setup
# No AWS credentials needed - EC2 will use IAM role

# AWS Configuration
AWS_REGION=ap-south-1

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# Application Configuration
APP_ENV=production
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# MCP Server Configuration Path
MCP_CONFIG_PATH=/opt/mcp-client/server_config.json
```

Save with `Ctrl+X`, `Y`, `Enter`

## Step 7: Create Systemd Service File

```bash
# Create the service file
sudo nano /etc/systemd/system/mcp-client.service
```

**Copy and paste this content:**

```ini
[Unit]
Description=MCP Client FastAPI Backend Service
Documentation=https://fastapi.tiangolo.com/
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/mcp-client
Environment=AWS_REGION=ap-south-1
Environment=BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
ExecStart=/opt/mcp-client/venv/bin/gunicorn main:app \
    --bind 0.0.0.0:8000 \
    --workers 2 \
    --worker-class uvicorn.workers.UvicornWorker \
    --timeout 300 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --preload \
    --access-logfile /var/log/mcp-client/access.log \
    --error-logfile /var/log/mcp-client/error.log \
    --log-level info
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings (simplified)
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
```

Save with `Ctrl+X`, `Y`, `Enter`

## Step 8: Set File Permissions

```bash
# Set proper permissions
sudo chmod 755 /opt/mcp-client
sudo chmod 644 /opt/mcp-client/main.py
sudo chmod 644 /opt/mcp-client/server_config.json
sudo chmod 644 /opt/mcp-client/requirements.txt
sudo chmod 600 /opt/mcp-client/.env
```

## Step 9: Enable and Start the Service

```bash
# Reload systemd to recognize new service
sudo systemctl daemon-reload

# Enable service to start on boot
sudo systemctl enable mcp-client

# Start the service
sudo systemctl start mcp-client
```

## Step 10: Verify Deployment

### Check Service Status

```bash
# Check if service is running
sudo systemctl status mcp-client
```

**Expected output:** `Active: active (running)` in green

### View Logs

```bash
# View real-time logs
sudo journalctl -u mcp-client -f

# View last 50 log lines
sudo journalctl -u mcp-client -n 50

# Press Ctrl+C to exit log viewing
```

**Expected logs:**
- "Bedrock client initialized successfully using IAM role"
- "Starting gunicorn"
- "Booting worker"

### Test Application

```bash
# Get your public IP
PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
echo "Your application is at: http://$PUBLIC_IP:8000"

# Test health endpoint locally
curl http://localhost:8000/

# Expected response: {"message": "MCP Client API is running", "status": "healthy"}

# Test from external IP
curl http://$PUBLIC_IP:8000/

# Test servers endpoint
curl http://$PUBLIC_IP:8000/servers

# Test tools endpoint
curl http://$PUBLIC_IP:8000/tools

# Test chat endpoint
curl -X POST http://$PUBLIC_IP:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?", "use_tools": false}'
```

### Test in Browser

Open your web browser and navigate to:
```
http://YOUR_EC2_PUBLIC_IP:8000
```

You should see:
```json
{"message": "MCP Client API is running", "status": "healthy"}
```

### Test API Documentation

```bash
# FastAPI automatically generates interactive API docs
echo "API Documentation: http://$PUBLIC_IP:8000/docs"
echo "Alternative docs: http://$PUBLIC_IP:8000/redoc"
```

## Service Management Commands

```bash
# Start service
sudo systemctl start mcp-client

# Stop service
sudo systemctl stop mcp-client

# Restart service
sudo systemctl restart mcp-client

# Check status
sudo systemctl status mcp-client

# Enable auto-start on boot
sudo systemctl enable mcp-client

# Disable auto-start
sudo systemctl disable mcp-client

# View real-time logs
sudo journalctl -u mcp-client -f

# View logs since today
sudo journalctl -u mcp-client --since today
```

## Troubleshooting

### Service Fails to Start

```bash
# Check detailed error logs
sudo journalctl -u mcp-client --no-pager

# Check if Python dependencies are installed
sudo /opt/mcp-client/venv/bin/pip list | grep fastapi

# Test running manually
cd /opt/mcp-client
sudo ./venv/bin/python main.py

# Check file permissions
ls -la /opt/mcp-client/
```

### Cannot Access from Browser

```bash
# Check if service is listening on port 8000
sudo netstat -tlnp | grep :8000

# Should show: 0.0.0.0:8000

# Check if port 8000 is open in AWS Security Group
# Go to EC2 Console → Security Groups → Your SG → Inbound rules
# Make sure port 8000 is open to 0.0.0.0/0
```

### AWS Access Issues

```bash
# Check if IAM role is attached
curl http://***************/latest/meta-data/iam/security-credentials/

# Should return your role name, not 404

# Test AWS access
aws sts get-caller-identity

# Test Bedrock access
aws bedrock list-foundation-models --region ap-south-1

# If commands fail, check IAM role permissions in AWS Console
```

### Python Import Errors

```bash
# Check Python version in virtual environment
sudo /opt/mcp-client/venv/bin/python --version

# Should be 3.10 or higher. If not, recreate venv:
sudo rm -rf /opt/mcp-client/venv
sudo python3 -m venv /opt/mcp-client/venv
sudo /opt/mcp-client/venv/bin/pip install --upgrade pip wheel
sudo /opt/mcp-client/venv/bin/pip install -r /opt/mcp-client/requirements.txt

# Reinstall dependencies if needed
cd /opt/mcp-client
sudo ./venv/bin/pip install -r requirements.txt --force-reinstall

# Check Python path
sudo ./venv/bin/python -c "import sys; print(sys.path)"

# Test specific imports
sudo ./venv/bin/python -c "import fastapi, boto3, mcp; print('All imports successful')"

# If MCP import fails, try installing latest version
sudo ./venv/bin/pip install mcp --upgrade
```

## File Structure

After successful deployment, your file structure should look like:

```
/opt/mcp-client/
├── main.py
├── server_config.json
├── requirements.txt
├── .env
└── venv/
    └── (Python virtual environment)

/var/log/mcp-client/
├── access.log
└── error.log

/etc/systemd/system/
└── mcp-client.service
```

## Success Checklist

- [ ] Service shows `Active: active (running)`
- [ ] `curl http://localhost:8000/` returns JSON response
- [ ] Browser shows API response at `http://YOUR_IP:8000`
- [ ] No errors in `sudo journalctl -u mcp-client -n 50`
- [ ] AWS access works: `aws sts get-caller-identity`
- [ ] Chat endpoint responds to test requests
- [ ] API documentation accessible at `/docs`

## Security Notes

- **IAM Role**: Application uses EC2 IAM role for AWS access (no credentials in files)
- **Firewall**: Ensure AWS Security Group allows port 8000
- **Updates**: Regularly update system packages with `sudo yum update`
- **Monitoring**: Monitor logs regularly with `sudo journalctl -u mcp-client -f`

## Performance Tuning

### Increase Workers (for higher traffic)

```bash
# Edit service file
sudo nano /etc/systemd/system/mcp-client.service

# Change: --workers 2 to --workers 4 (or more)

# Reload and restart
sudo systemctl daemon-reload
sudo systemctl restart mcp-client
```

### Monitor Resource Usage

```bash
# Real-time system monitoring
htop

# Check memory usage
free -h

# Check disk usage
df -h

# Monitor application logs
sudo tail -f /var/log/mcp-client/access.log
```

## Congratulations! 🎉

Your MCP Client is now successfully deployed and running at:
**http://YOUR_EC2_PUBLIC_IP:8000**

The application features:
- ✅ **Secure IAM role authentication**
- ✅ **Production-ready Gunicorn server**
- ✅ **Automatic restart on failure**
- ✅ **Comprehensive logging**
- ✅ **Interactive API documentation**
- ✅ **Auto-start on system boot**
