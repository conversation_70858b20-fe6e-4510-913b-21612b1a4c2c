#!/bin/bash

# MCP Client Deployment Script for Amazon Linux
# Run this script as root or with sudo privileges

set -e  # Exit on any error

echo "Starting MCP Client deployment on Amazon Linux..."

# Configuration
APP_NAME="mcp-client"
APP_DIR="/opt/${APP_NAME}"
LOG_DIR="/var/log/${APP_NAME}"
SERVICE_FILE="${APP_NAME}.service"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Detect Amazon Linux version
if grep -q "Amazon Linux 2023" /etc/os-release; then
    AMAZON_LINUX_VERSION="2023"
    PACKAGE_MANAGER="dnf"
elif grep -q "Amazon Linux 2" /etc/os-release; then
    AMAZON_LINUX_VERSION="2"
    PACKAGE_MANAGER="yum"
else
    log_error "Unsupported Amazon Linux version"
    exit 1
fi

log_info "Deploying on Amazon Linux $AMAZON_LINUX_VERSION"

# Update system packages
log_info "Updating system packages..."
$PACKAGE_MANAGER update -y

# Install Python and development tools if not already installed
log_info "Ensuring Python development tools are installed..."
if [ "$AMAZON_LINUX_VERSION" = "2023" ]; then
    dnf install -y python3 python3-pip python3-devel gcc
else
    yum install -y python3 python3-pip python3-devel gcc
fi

# Create application directory
log_info "Setting up application directory: ${APP_DIR}"
mkdir -p ${APP_DIR}

# Create log directory
log_info "Creating log directory: ${LOG_DIR}"
mkdir -p ${LOG_DIR}

# Copy application files
log_info "Copying application files..."
if [ ! -f "main.py" ]; then
    log_error "main.py not found in current directory"
    exit 1
fi

cp main.py ${APP_DIR}/
cp server_config.json ${APP_DIR}/
cp requirements.txt ${APP_DIR}/

# Copy environment file
if [ -f ".env.production" ]; then
    cp .env.production ${APP_DIR}/.env
elif [ -f ".env" ]; then
    cp .env ${APP_DIR}/.env
else
    log_warn "No .env file found, creating template..."
    cat > ${APP_DIR}/.env <<EOF
# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# Application Configuration
APP_ENV=production
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000
EOF
fi

# Setup Python virtual environment
log_info "Setting up Python virtual environment..."
python3 -m venv ${APP_DIR}/venv
${APP_DIR}/venv/bin/pip install --upgrade pip

# Install Python dependencies
log_info "Installing Python dependencies..."
${APP_DIR}/venv/bin/pip install -r ${APP_DIR}/requirements.txt

# Set proper permissions
log_info "Setting file permissions..."
chmod -R 755 ${APP_DIR}
chmod 600 ${APP_DIR}/.env

# Install systemd service
log_info "Installing systemd service..."
if [ ! -f "${SERVICE_FILE}" ]; then
    log_warn "Service file not found, creating default service file..."
    cat > /etc/systemd/system/${SERVICE_FILE} <<EOF
[Unit]
Description=MCP Client FastAPI Backend Service
Documentation=https://fastapi.tiangolo.com/
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=${APP_DIR}
ExecStart=${APP_DIR}/venv/bin/gunicorn main:app \\
    --bind 0.0.0.0:8000 \\
    --workers 2 \\
    --worker-class uvicorn.workers.UvicornWorker \\
    --timeout 300 \\
    --max-requests 1000 \\
    --max-requests-jitter 100 \\
    --preload \\
    --access-logfile ${LOG_DIR}/access.log \\
    --error-logfile ${LOG_DIR}/error.log \\
    --log-level info
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=${APP_DIR} ${LOG_DIR} /tmp
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

[Install]
WantedBy=multi-user.target
EOF
else
    cp ${SERVICE_FILE} /etc/systemd/system/
fi

systemctl daemon-reload
systemctl enable ${SERVICE_FILE}

# Configure log rotation
log_info "Setting up log rotation..."
cat > /etc/logrotate.d/mcp-client <<EOF
${LOG_DIR}/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload mcp-client
    endscript
}
EOF

log_info "Deployment completed successfully!"
echo ""
log_warn "IMPORTANT: Please update the .env file with your actual AWS credentials:"
log_warn "  sudo nano ${APP_DIR}/.env"
echo ""
log_info "To start the service:"
log_info "  sudo systemctl start ${APP_NAME}"
echo ""
log_info "To check status:"
log_info "  sudo systemctl status ${APP_NAME}"
echo ""
log_info "To view logs:"
log_info "  sudo journalctl -u ${APP_NAME} -f"
echo ""
log_info "Your application will be accessible at:"
log_info "  http://$(curl -s http://***************/latest/meta-data/public-ipv4):8000"
echo ""
log_warn "Remember to update your EC2 Security Group to allow inbound traffic on port 8000"
