"""
FastAPI Backend for MCP Client with Bedrock Integration - FIXED VERSION
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import asyncio
import json
import logging
from contextlib import asynccontextmanager
import uvicorn
import boto3
from botocore.config import Config
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import subprocess
import sys
import os
from dotenv import load_dotenv
import uuid
import weakref
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic Models
class MCPServerConfig(BaseModel):
    name: str
    command: str
    args: List[str] = []
    env: Dict[str, str] = {}
    description: str = ""
    enabled: bool = True

class ChatMessage(BaseModel):
    role: str
    content: str
    timestamp: Optional[str] = None

class ChatRequest(BaseModel):
    message: str
    conversation_id: Optional[str] = None
    use_tools: bool = True

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    tools_used: List[Dict[str, Any]] = []
    status: str = "success"

class MCPServerConnection:
    """Manages individual MCP server connection"""
    def __init__(self, config: MCPServerConfig):
        self.config = config
        self.session: Optional[ClientSession] = None
        self.stdio_context = None
        self.tools = []
        self.resources = []
        self.status = "disconnected"
        self.error = None
        self._cleanup_tasks = []
        
    async def connect(self):
        """Connect to the MCP server"""
        try:
            self.status = "connecting"
            logger.info(f"Connecting to MCP server: {self.config.name}")
            
            # Create server parameters
            server_params = StdioServerParameters(
                command=self.config.command,
                args=self.config.args,
                env=self.config.env
            )
            
            # Use proper context manager
            self.stdio_context = stdio_client(server_params)
            read, write = await self.stdio_context.__aenter__()
            
            # Create and initialize session
            self.session = ClientSession(read, write)
            await self.session.__aenter__()
            
            # Initialize the session
            init_result = await self.session.initialize()
            logger.info(f"Session initialized for {self.config.name}")
            
            # List tools
            await self._list_tools()
            
            # List resources  
            await self._list_resources()
            
            self.status = "connected"
            self.error = None
            logger.info(f"Successfully connected to MCP server: {self.config.name} with {len(self.tools)} tools")
            return True
            
        except Exception as e:
            self.status = "error"
            self.error = str(e)
            logger.error(f"Failed to connect to MCP server {self.config.name}: {e}")
            await self.disconnect()
            return False
    
    async def _list_tools(self):
        """List tools from the server"""
        try:
            if not self.session:
                return
                
            tools_result = await self.session.list_tools()
            self.tools = []
            
            if hasattr(tools_result, 'tools') and tools_result.tools:
                for tool in tools_result.tools:
                    # Extract input schema properly
                    input_schema = {}
                    if hasattr(tool, 'inputSchema'):
                        input_schema = tool.inputSchema
                    elif hasattr(tool, 'input_schema'):
                        input_schema = tool.input_schema
                    
                    tool_info = {
                        "name": tool.name,
                        "description": getattr(tool, 'description', ''),
                        "input_schema": input_schema
                    }
                    self.tools.append(tool_info)
                    logger.info(f"Found tool: {tool.name} on server {self.config.name}")
                    
        except Exception as e:
            logger.error(f"Error listing tools for {self.config.name}: {e}")
    
    async def _list_resources(self):
        """List resources from the server"""
        try:
            if not self.session:
                return
                
            resources_result = await self.session.list_resources()
            self.resources = []
            
            if hasattr(resources_result, 'resources') and resources_result.resources:
                self.resources = [
                    {
                        "uri": resource.uri,
                        "name": getattr(resource, 'name', ''),
                        "description": getattr(resource, 'description', ''),
                        "mimeType": getattr(resource, 'mimeType', '')
                    }
                    for resource in resources_result.resources
                ]
                logger.info(f"Found {len(self.resources)} resources on server {self.config.name}")
                
        except Exception as e:
            logger.error(f"Error listing resources for {self.config.name}: {e}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on this server"""
        try:
            if not self.session or self.status != "connected":
                return {
                    "success": False,
                    "error": f"Server {self.config.name} is not connected (status: {self.status})",
                    "tool_name": tool_name,
                    "server_name": self.config.name
                }
            
            # Find the tool
            tool_info = None
            for tool in self.tools:
                if tool["name"] == tool_name:
                    tool_info = tool
                    break
            
            if not tool_info:
                available_tools = [t["name"] for t in self.tools]
                return {
                    "success": False,
                    "error": f"Tool {tool_name} not found on server {self.config.name}. Available: {available_tools}",
                    "tool_name": tool_name,
                    "server_name": self.config.name
                }
            
            # Validate arguments against schema
            tool_schema = tool_info.get("input_schema", {})
            if tool_schema and "required" in tool_schema:
                for req_field in tool_schema["required"]:
                    if req_field not in arguments:
                        return {
                            "success": False,
                            "error": f"Missing required field: {req_field}",
                            "tool_name": tool_name,
                            "server_name": self.config.name
                        }
            
            logger.info(f"Calling tool {tool_name} on {self.config.name} with arguments: {arguments}")
            
            # Call the tool
            result = await self.session.call_tool(tool_name, arguments)
            
            # Handle different result formats
            if hasattr(result, 'content'):
                content = result.content
                if isinstance(content, list):
                    # Extract text from content list
                    text_parts = []
                    for item in content:
                        if hasattr(item, 'text'):
                            text_parts.append(item.text)
                        elif isinstance(item, dict) and 'text' in item:
                            text_parts.append(item['text'])
                        else:
                            text_parts.append(str(item))
                    content = '\n'.join(text_parts) if text_parts else str(content)
                
                logger.info(f"Tool {tool_name} returned: {content}")
                return {
                    "success": True,
                    "result": content,
                    "tool_name": tool_name,
                    "server_name": self.config.name
                }
            else:
                result_str = str(result)
                logger.info(f"Tool {tool_name} returned: {result_str}")
                return {
                    "success": True,
                    "result": result_str,
                    "tool_name": tool_name,
                    "server_name": self.config.name
                }
                
        except Exception as e:
            logger.error(f"Tool execution error for {tool_name} on {self.config.name}: {e}")
            return {
                "success": False,
                "error": f"Tool execution failed: {str(e)}",
                "tool_name": tool_name,
                "server_name": self.config.name
            }
    
    async def disconnect(self):
        """Disconnect from the server - Fixed cleanup"""
        try:
            logger.info(f"Starting disconnect for {self.config.name}")
            
            # First, safely close the session
            if self.session:
                try:
                    # Use a timeout to prevent hanging
                    await asyncio.wait_for(
                        self.session.__aexit__(None, None, None),
                        timeout=5.0
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"Session cleanup timed out for {self.config.name}")
                except Exception as e:
                    logger.warning(f"Error during session cleanup for {self.config.name}: {e}")
                finally:
                    self.session = None
            
            # Then close the stdio context
            if self.stdio_context:
                try:
                    await asyncio.wait_for(
                        self.stdio_context.__aexit__(None, None, None),
                        timeout=5.0
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"Stdio context cleanup timed out for {self.config.name}")
                except Exception as e:
                    logger.warning(f"Error during stdio context cleanup for {self.config.name}: {e}")
                finally:
                    self.stdio_context = None
            
            self.status = "disconnected"
            logger.info(f"Successfully disconnected from {self.config.name}")
            
        except Exception as e:
            logger.error(f"Error during disconnect from {self.config.name}: {e}")
            # Force cleanup even if there were errors
            self.session = None
            self.stdio_context = None
            self.status = "error"


class MCPClientManager:
    def __init__(self):
        self.connections: Dict[str, MCPServerConnection] = {}
        self.bedrock_client = None
        self._initialize_bedrock()
    
    def _initialize_bedrock(self):
        """Initialize AWS Bedrock client"""
        try:
            # Configure with retry settings for better reliability
            config = Config(
                region_name=os.getenv('AWS_REGION', 'ap-south-1'),
                retries={'max_attempts': 3, 'mode': 'adaptive'}
            )
            
            self.bedrock_client = boto3.client(
                'bedrock-runtime',
                config=config,
                aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
                aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY')
            )
            logger.info("Bedrock client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock client: {e}")
            raise

    async def add_server(self, config: MCPServerConfig) -> bool:
        """Add and connect to an MCP server"""
        # Create connection object
        connection = MCPServerConnection(config)
        self.connections[config.name] = connection
        
        # Attempt to connect
        success = await connection.connect()
        return success

    def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """Get all available tools from all connected servers"""
        tools = {}
        for server_name, connection in self.connections.items():
            if connection.status == "connected":
                for tool in connection.tools:
                    tool_key = f"{server_name}::{tool['name']}"
                    tools[tool_key] = {
                        "server": server_name,
                        "tool": tool,
                        "connection": connection
                    }
        return tools

    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on a specific MCP server"""
        if server_name not in self.connections:
            return {
                "success": False,
                "error": f"Server {server_name} not found",
                "tool_name": tool_name,
                "server_name": server_name
            }
        
        connection = self.connections[server_name]
        
        if connection.status != "connected":
            # Try to reconnect
            logger.info(f"Attempting to reconnect to {server_name}")
            success = await connection.connect()
            if not success:
                return {
                    "success": False,
                    "error": f"Server {server_name} is not connected and reconnection failed: {connection.error}",
                    "tool_name": tool_name,
                    "server_name": server_name
                }
        
        return await connection.call_tool(tool_name, arguments)

    async def chat_with_bedrock(self, message: str, tools_available: List[str] = None) -> str:
        """Chat with Bedrock with fixed multiple tool call handling"""
        try:
            model_id = os.getenv('BEDROCK_MODEL_ID')
            print(model_id)
            # Get current available tools
            available_tools = self.get_available_tools()
            
            system_message = "You are a helpful AI assistant with access to various tools."
            if available_tools:
                tools_info = "\n\nAvailable tools:\n"
                for tool_key, tool_data in available_tools.items():
                    tool = tool_data["tool"]
                    tools_info += f"- {tool['name']} (server: {tool_data['server']}): {tool['description']}\n"
                system_message += tools_info

            messages = [{
                "role": "user",
                "content": [{"text": message}]
            }]

            inference_config = {
                "maxTokens": 4000,
                "temperature": 0.7,
                "topP": 0.9
            }

            system = [{"text": system_message}]

            tool_config = None
            if tools_available and available_tools:
                tools = []
                for tool_key, tool_data in available_tools.items():
                    tool = tool_data["tool"]
                    
                    # Ensure proper schema format for Bedrock
                    input_schema = tool.get("input_schema", {})
                    if not input_schema:
                        input_schema = {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }
                    
                    # Make sure the schema has the required structure
                    if "type" not in input_schema:
                        input_schema["type"] = "object"
                    if "properties" not in input_schema:
                        input_schema["properties"] = {}
                    
                    tools.append({
                        "toolSpec": {
                            "name": tool["name"],
                            "description": tool["description"] or f"Tool from server {tool_data['server']}",
                            "inputSchema": {
                                "json": input_schema
                            }
                        }
                    })
                
                tool_config = {
                    "tools": tools,
                    "toolChoice": {"auto": {}}
                }
                
                logger.info(f"Configured {len(tools)} tools for Bedrock")

            # Keep conversing until we get an end turn
            max_iterations = 10  # Prevent infinite loops
            iteration = 0
            
            while iteration < max_iterations:
                iteration += 1
                logger.info(f"Bedrock conversation iteration {iteration}")
                
                converse_params = {
                    "modelId": model_id,
                    "messages": messages,
                    "system": system,
                    "inferenceConfig": inference_config
                }

                if tool_config:
                    converse_params["toolConfig"] = tool_config

                response = self.bedrock_client.converse(**converse_params)
                stop_reason = response.get('stopReason')
                logger.info(f"Stop reason: {stop_reason}")

                # Handle tool use response - Fixed to continue until end_turn
                if stop_reason == 'tool_use':
                    assistant_message = response.get('output', {}).get('message', {})
                    messages.append(assistant_message)
                    
                    tool_results = []
                    
                    for content in assistant_message.get('content', []):
                        if content.get('toolUse'):
                            tool_use = content['toolUse']
                            tool_name = tool_use.get('name')
                            tool_input = tool_use.get('input', {})
                            tool_use_id = tool_use.get('toolUseId')

                            logger.info(f"Tool use requested: {tool_name} with input: {tool_input}")

                            # Find the server for this tool
                            server_name = None
                            for tool_key, tool_data in available_tools.items():
                                if tool_data["tool"]["name"] == tool_name:
                                    server_name = tool_data["server"]
                                    break

                            if server_name:
                                result = await self.call_tool(server_name, tool_name, tool_input)
                                logger.info(f"Tool result: {result}")
                                
                                # Format result for Bedrock
                                result_content = result.get("result", str(result)) if result["success"] else f"Error: {result.get('error', 'Unknown error')}"
                                
                                tool_results.append({
                                    "toolUseId": tool_use_id,
                                    "content": [{"text": str(result_content)}],
                                    "status": "success" if result["success"] else "error"
                                })
                            else:
                                tool_results.append({
                                    "toolUseId": tool_use_id,
                                    "content": [{"text": f"Error: Tool {tool_name} not found"}],
                                    "status": "error"
                                })

                    # Add tool results to conversation
                    if tool_results:
                        tool_result_message = {
                            "role": "user",
                            "content": []
                        }

                        for tool_result in tool_results:
                            tool_result_message["content"].append({
                                "toolResult": {
                                    "toolUseId": tool_result["toolUseId"],
                                    "content": tool_result["content"],
                                    "status": tool_result["status"]
                                }
                            })

                        messages.append(tool_result_message)
                        
                    # Continue the loop to get next response
                    continue
                    
                elif stop_reason == 'end_turn' or stop_reason == 'stop_sequence' or stop_reason == 'max_tokens':
                    # This is the final response
                    output_message = response.get('output', {}).get('message', {})
                    for content in output_message.get('content', []):
                        if content.get('text'):
                            return content['text']
                    
                    # If no text content found, return a default message
                    return 'Response completed.'
                    
                else:
                    # Unexpected stop reason, handle as final response
                    logger.warning(f"Unexpected stop reason: {stop_reason}")
                    output_message = response.get('output', {}).get('message', {})
                    for content in output_message.get('content', []):
                        if content.get('text'):
                            return content['text']
                    return f'Response completed with stop reason: {stop_reason}'

            # If we hit max iterations
            return 'Response completed after maximum iterations.'
            
        except Exception as e:
            logger.error(f"Error in Bedrock chat: {e}")
            return f"Error: {str(e)}"

    async def cleanup(self):
        """Clean up all connections - Fixed scope issue"""
        logger.info("Starting cleanup of all connections")
        
        # Create a list to avoid modifying dict during iteration
        connections_to_cleanup = list(self.connections.items())
        
        # Cleanup each connection with proper error handling
        for name, connection in connections_to_cleanup:
            try:
                logger.info(f"Cleaning up connection: {name}")
                await connection.disconnect()
            except Exception as e:
                logger.error(f"Error cleaning up connection {name}: {e}")
        
        # Clear the connections dict
        self.connections.clear()
        logger.info("Cleanup completed")


# Global MCP client manager
mcp_manager = MCPClientManager()

# Lifespan context manager - Fixed cleanup
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan"""
    # Startup
    logger.info("Starting MCP Client API")
    try:
        yield
    finally:
        # Shutdown - Run cleanup in a separate task to avoid scope issues
        logger.info("Shutting down MCP Client API")
        try:
            await mcp_manager.cleanup()
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

# Create FastAPI app
app = FastAPI(
    title="MCP Client API",
    description="Multi-server MCP client with Bedrock integration",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# API Endpoints
@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "MCP Client API is running", "status": "healthy"}

@app.get("/servers")
async def list_servers():
    """List all configured MCP servers"""
    servers_info = {}
    for name, connection in mcp_manager.connections.items():
        servers_info[name] = {
            "name": name,
            "status": connection.status,
            "tools_count": len(connection.tools),
            "resources_count": len(connection.resources),
            "description": connection.config.description,
            "enabled": connection.config.enabled,
            "error": connection.error
        }
    return servers_info

@app.post("/servers")
async def add_server(config: MCPServerConfig):
    """Add a new MCP server"""
    success = await mcp_manager.add_server(config)
    if success:
        return {"message": f"Server {config.name} added successfully"}
    else:
        connection = mcp_manager.connections.get(config.name)
        error_msg = connection.error if connection else "Unknown error"
        raise HTTPException(status_code=400, detail=f"Failed to add server {config.name}: {error_msg}")

@app.get("/servers/{server_name}")
async def get_server_details(server_name: str):
    """Get detailed information about a specific server"""
    if server_name not in mcp_manager.connections:
        raise HTTPException(status_code=404, detail="Server not found")
    
    connection = mcp_manager.connections[server_name]
    return {
        "name": server_name,
        "status": connection.status,
        "config": connection.config.dict(),
        "tools": connection.tools,
        "resources": connection.resources,
        "error": connection.error
    }

@app.get("/tools")
async def list_tools():
    """List all available tools across all servers"""
    tools = {}
    available_tools = mcp_manager.get_available_tools()
    for tool_key, tool_data in available_tools.items():
        tools[tool_key] = {
            "server": tool_data["server"],
            "name": tool_data["tool"]["name"],
            "description": tool_data["tool"]["description"],
            "input_schema": tool_data["tool"].get("input_schema", {})
        }
    return tools

@app.post("/tools/call")
async def call_tool_endpoint(server_name: str, tool_name: str, arguments: Dict[str, Any]):
    """Call a specific tool"""
    result = await mcp_manager.call_tool(server_name, tool_name, arguments)
    if result["success"]:
        return result
    else:
        raise HTTPException(status_code=400, detail=result["error"])

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Main chat endpoint with tool integration"""
    try:
        # Get conversation ID (generate if not provided)
        conversation_id = request.conversation_id or f"conv_{uuid.uuid4().hex[:8]}"
        
        # Use tools if requested and available
        available_tools = mcp_manager.get_available_tools()
        tools_available = list(available_tools.keys()) if request.use_tools else []
        
        # Get response from Bedrock
        response = await mcp_manager.chat_with_bedrock(
            request.message, 
            tools_available
        )
        
        return ChatResponse(
            response=response,
            conversation_id=conversation_id,
            tools_used=[],  # This could be populated with actual tool usage tracking
            status="success"
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Additional endpoint for reconnecting to a server
@app.post("/servers/{server_name}/reconnect")
async def reconnect_server(server_name: str):
    """Reconnect to a specific server"""
    if server_name not in mcp_manager.connections:
        raise HTTPException(status_code=404, detail="Server not found")
    
    connection = mcp_manager.connections[server_name]
    
    # Disconnect first if connected
    if connection.status == "connected":
        await connection.disconnect()
    
    # Reconnect
    success = await connection.connect()
    if success:
        return {"message": f"Server {server_name} reconnected successfully"}
    else:
        raise HTTPException(status_code=500, detail=f"Failed to reconnect to server {server_name}: {connection.error}")

# Debug endpoint to check connection states
@app.get("/debug/connections")
async def debug_connections():
    """Debug endpoint to check connection states"""
    debug_info = {}
    for name, connection in mcp_manager.connections.items():
        debug_info[name] = {
            "status": connection.status,
            "error": connection.error,
            "tools_count": len(connection.tools),
            "session_exists": connection.session is not None,
            "stdio_context_exists": connection.stdio_context is not None,
            "tools": [tool["name"] for tool in connection.tools]
        }
    return debug_info

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )