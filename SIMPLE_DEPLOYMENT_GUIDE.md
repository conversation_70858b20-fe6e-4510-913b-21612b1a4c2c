# Simple MCP Client Deployment Guide (No Nginx)

This guide provides a simplified deployment approach for your MCP Client FastAPI application on AWS EC2 without Nginx reverse proxy.

## Prerequisites

- AWS Account with appropriate permissions
- AWS CLI configured locally (for file transfer)
- SSH key pair for EC2 access

## Step 1: Launch EC2 Instance

### Recommended Instance Configuration

**Instance Type**: `t3.small` or `t3.medium`
- **vCPUs**: 2
- **Memory**: 2-4 GB RAM
- **Storage**: 20 GB GP3 SSD

**Operating System**: Ubuntu 22.04 LTS

### Security Group Configuration

Create a security group with the following rules:

| Type | Protocol | Port Range | Source | Description |
|------|----------|------------|--------|-------------|
| SSH | TCP | 22 | Your IP | SSH access |
| Custom TCP | TCP | 8000 | 0.0.0.0/0 | FastAPI application |

### Launch Instance
```bash
aws ec2 run-instances \
  --image-id ami-0c02fb55956c7d316 \
  --instance-type t3.small \
  --key-name your-key-pair \
  --security-group-ids sg-xxxxxxxxx \
  --subnet-id subnet-xxxxxxxxx \
  --associate-public-ip-address \
  --block-device-mappings '[{"DeviceName":"/dev/sda1","Ebs":{"VolumeSize":20,"VolumeType":"gp3"}}]' \
  --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=MCP-Client-Simple}]'
```

## Step 2: Connect to EC2 Instance

```bash
ssh -i your-key.pem ubuntu@your-instance-public-ip
```

## Step 3: Initial Server Setup

Run the simple setup script:

```bash
# Download and run the setup script
wget https://raw.githubusercontent.com/your-repo/simple-setup-ec2.sh
chmod +x simple-setup-ec2.sh
sudo ./simple-setup-ec2.sh
```

## Step 4: Upload Application Files

From your local machine:

```bash
# Create deployment package
tar -czf mcp-client-simple.tar.gz \
  main.py \
  server_config.json \
  requirements.txt \
  .env.production \
  mcp-client.service \
  simple-deploy.sh

# Upload to EC2
scp -i your-key.pem mcp-client-simple.tar.gz ubuntu@your-instance-ip:~/

# Extract on server
ssh -i your-key.pem ubuntu@your-instance-ip
tar -xzf mcp-client-simple.tar.gz
```

## Step 5: Deploy Application

Run the simple deployment script:

```bash
sudo ./simple-deploy.sh
```

## Step 6: Configure Environment

Edit the environment file with your AWS credentials:

```bash
sudo nano /opt/mcp-client/.env
```

Update these variables:
```bash
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=ap-south-1
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
```

## Step 7: Start the Service

```bash
# Start the service
sudo systemctl start mcp-client

# Check status
sudo systemctl status mcp-client

# View logs
sudo journalctl -u mcp-client -f
```

## Step 8: Test Your Application

### Health Check
```bash
curl http://your-ec2-public-ip:8000/
```

### Test API Endpoints
```bash
# List servers
curl http://your-ec2-public-ip:8000/servers

# List tools
curl http://your-ec2-public-ip:8000/tools

# Test chat endpoint
curl -X POST http://your-ec2-public-ip:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?", "use_tools": false}'
```

## Service Management

### Basic Commands
```bash
# Start service
sudo systemctl start mcp-client

# Stop service
sudo systemctl stop mcp-client

# Restart service
sudo systemctl restart mcp-client

# Check status
sudo systemctl status mcp-client

# View logs
sudo journalctl -u mcp-client -f

# Enable auto-start on boot
sudo systemctl enable mcp-client
```

### Update Application
```bash
# Stop service
sudo systemctl stop mcp-client

# Update main.py
sudo cp new-main.py /opt/mcp-client/main.py

# Restart service
sudo systemctl start mcp-client
```

## Monitoring

### Check Application Logs
```bash
# Real-time logs
sudo journalctl -u mcp-client -f

# Last 100 lines
sudo journalctl -u mcp-client -n 100

# Logs from today
sudo journalctl -u mcp-client --since today
```

### System Monitoring
```bash
# Check system resources
htop

# Check disk usage
df -h

# Check memory usage
free -h

# Check network connections
sudo netstat -tlnp | grep :8000
```

## Troubleshooting

### Common Issues

1. **Service won't start**:
   ```bash
   sudo journalctl -u mcp-client --no-pager
   ```

2. **Port already in use**:
   ```bash
   sudo netstat -tlnp | grep :8000
   sudo kill -9 <PID>
   ```

3. **Permission errors**:
   ```bash
   sudo chmod -R 755 /opt/mcp-client
   sudo chmod 600 /opt/mcp-client/.env
   ```

4. **AWS credentials issues**:
   ```bash
   # Test credentials
   aws sts get-caller-identity
   
   # Check environment file
   sudo cat /opt/mcp-client/.env
   ```

5. **Can't access from browser**:
   - Check EC2 Security Group allows port 8000
   - Verify UFW firewall: `sudo ufw status`
   - Check if service is running: `sudo systemctl status mcp-client`

### Performance Tuning

1. **Increase workers** (edit service file):
   ```bash
   sudo nano /etc/systemd/system/mcp-client.service
   # Change: --workers 4 to --workers 8
   sudo systemctl daemon-reload
   sudo systemctl restart mcp-client
   ```

2. **Monitor resource usage**:
   ```bash
   htop
   sudo journalctl -u mcp-client | grep -i error
   ```

## Security Considerations

### Basic Security (Already Configured)
- ✅ UFW firewall enabled
- ✅ Fail2ban for SSH protection
- ✅ Automatic security updates
- ✅ Non-root application execution
- ✅ Secure file permissions

### Additional Security (Optional)
1. **Change SSH port**:
   ```bash
   sudo nano /etc/ssh/sshd_config
   # Change Port 22 to Port 2222
   sudo systemctl restart sshd
   sudo ufw allow 2222/tcp
   sudo ufw delete allow ssh
   ```

2. **Restrict access by IP**:
   ```bash
   # Allow only your IP for port 8000
   sudo ufw delete allow 8000/tcp
   sudo ufw allow from YOUR_IP to any port 8000
   ```

## Backup

### Simple Backup Script
```bash
sudo tee /opt/backup.sh > /dev/null <<EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
tar -czf /tmp/mcp-client-backup-\$DATE.tar.gz /opt/mcp-client
# Upload to S3 (optional)
# aws s3 cp /tmp/mcp-client-backup-\$DATE.tar.gz s3://your-backup-bucket/
EOF

sudo chmod +x /opt/backup.sh
```

### Automated Backups
```bash
# Add to crontab for daily backups
echo "0 2 * * * /opt/backup.sh" | sudo crontab -
```

## Cost Optimization

- Start with `t3.small` instance
- Monitor CPU/memory usage with `htop`
- Scale up to `t3.medium` if needed
- Use Reserved Instances for long-term deployments

Your application will be accessible at: **http://YOUR_EC2_PUBLIC_IP:8000**

This simple setup is perfect for development, testing, or small-scale production deployments without the complexity of reverse proxies.
