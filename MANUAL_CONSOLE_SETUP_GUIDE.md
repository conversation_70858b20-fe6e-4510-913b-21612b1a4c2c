# Manual AWS Console Setup Guide for MCP Client

This guide walks you through setting up your MCP Client deployment manually using the AWS Console.

## Part 1: Create IAM Policy and Role

### Step 1: Create IAM Policy

1. **Open AWS Console** → Search for **"IAM"** → Click **IAM**
2. In the left sidebar, click **"Policies"**
3. Click **"Create policy"** button
4. Click the **"JSON"** tab
5. **Delete** the existing content and **paste** this policy:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": [
                "arn:aws:bedrock:*:*:foundation-model/anthropic.claude-*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "pricing:GetProducts",
                "pricing:DescribeServices"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "ce:GetCostAndUsage",
                "ce:GetUsageReport",
                "ce:GetDimensionValues"
            ],
            "Resource": "*"
        }
    ]
}
```

6. Click **"Next: Tags"** (skip tags for now)
7. Click **"Next: Review"**
8. **Policy name**: `MCP-Client-Policy`
9. **Description**: `Policy for MCP Client to access Bedrock and AWS services`
10. Click **"Create policy"**

### Step 2: Create IAM Role

1. In IAM console, click **"Roles"** in the left sidebar
2. Click **"Create role"** button
3. **Trusted entity type**: Select **"AWS service"**
4. **Service or use case**: Select **"EC2"**
5. **Use case**: Select **"EC2"** (should be pre-selected)
6. Click **"Next"**
7. In the **"Permissions policies"** search box, type: `MCP-Client-Policy`
8. **Check the box** next to your `MCP-Client-Policy`
9. Click **"Next"**
10. **Role name**: `MCP-Client-EC2-Role`
11. **Description**: `IAM role for EC2 instances running MCP Client`
12. Click **"Create role"**

## Part 2: Create Security Group

### Step 3: Create Security Group

1. **Open AWS Console** → Search for **"EC2"** → Click **EC2**
2. In the left sidebar, under **"Network & Security"**, click **"Security Groups"**
3. Click **"Create security group"** button
4. **Security group name**: `mcp-client-sg`
5. **Description**: `Security group for MCP Client application`
6. **VPC**: Leave default (or select your preferred VPC)

### Step 4: Add Inbound Rules

7. In the **"Inbound rules"** section, click **"Add rule"**
8. **First rule (SSH)**:
   - **Type**: SSH
   - **Protocol**: TCP
   - **Port range**: 22
   - **Source**: My IP (or your specific IP address)
   - **Description**: SSH access

9. Click **"Add rule"** again
10. **Second rule (Application)**:
    - **Type**: Custom TCP
    - **Protocol**: TCP
    - **Port range**: 8000
    - **Source**: Anywhere-IPv4 (0.0.0.0/0)
    - **Description**: MCP Client application

11. Click **"Create security group"**

## Part 3: Launch EC2 Instance

### Step 5: Launch EC2 Instance

1. In EC2 console, click **"Launch instance"** button
2. **Name**: `MCP-Client-Server`

### Step 6: Choose AMI and Instance Type

3. **Application and OS Images (Amazon Machine Image)**:
   - Select **"Amazon Linux"**
   - Choose **"Amazon Linux 2023 AMI"** (recommended) or **"Amazon Linux 2 AMI"**

4. **Instance type**: 
   - Select **"t3.small"** (recommended) or **"t3.medium"** for better performance

### Step 7: Key Pair

5. **Key pair (login)**:
   - If you have an existing key pair: Select it from dropdown
   - If you need a new key pair: Click **"Create new key pair"**
     - **Key pair name**: `mcp-client-key`
     - **Key pair type**: RSA
     - **Private key file format**: .pem
     - Click **"Create key pair"** (this will download the .pem file)

### Step 8: Network Settings

6. **Network settings**: Click **"Edit"**
   - **VPC**: Leave default (or select your preferred VPC)
   - **Subnet**: Leave default (or select your preferred subnet)
   - **Auto-assign public IP**: Enable
   - **Firewall (security groups)**: Select existing security group
   - **Common security groups**: Select `mcp-client-sg` (the one you created)

### Step 9: Storage

7. **Configure storage**:
   - **Size (GiB)**: 20 GB (default is fine)
   - **Volume type**: gp3 (recommended for better performance)

### Step 10: Advanced Details (Important!)

8. **Advanced details**: Click to expand
9. **IAM instance profile**: Select **`MCP-Client-EC2-Role`** (the role you created)
10. Leave other settings as default

### Step 11: Launch

11. **Summary**: Review your settings
12. Click **"Launch instance"**
13. Wait for the instance to be in **"Running"** state (takes 1-2 minutes)

## Part 4: Connect to Your Instance

### Step 12: Get Connection Information

1. In EC2 console, click on your instance
2. Note the **"Public IPv4 address"** (you'll need this)
3. Click **"Connect"** button
4. Choose **"SSH client"** tab
5. Follow the instructions shown (or use the command below)

### Step 13: Connect via SSH

```bash
# Make sure your key file has correct permissions
chmod 400 mcp-client-key.pem

# Connect to your instance (replace with your actual IP)
ssh -i mcp-client-key.pem ec2-user@YOUR_INSTANCE_PUBLIC_IP
```

## Part 5: Deploy Your Application

### Step 14: Create Application Files

Once connected to your instance, create your application files:

```bash
# Create main.py
nano main.py
```

**Copy and paste your entire `main.py` content**, then save with `Ctrl+X`, `Y`, `Enter`

```bash
# Create server_config.json
nano server_config.json
```

**Copy and paste your `server_config.json` content**, then save

```bash
# Create requirements.txt
nano requirements.txt
```

**Copy and paste your `requirements.txt` content**, then save

### Step 15: Create Deployment Script

```bash
# Create the deployment script
nano deploy.sh
```

**Copy and paste the `amazon-linux-deploy.sh` content**, then save

### Step 16: Run Deployment

```bash
# Make the script executable
chmod +x deploy.sh

# Run the deployment script
sudo ./deploy.sh
```

The script will:
- Install Python and dependencies
- Create application directory
- Set up virtual environment
- Install Python packages
- Create systemd service
- Configure firewall

### Step 17: Start the Service

```bash
# Start the MCP Client service
sudo systemctl start mcp-client

# Check if it's running
sudo systemctl status mcp-client

# Enable auto-start on boot
sudo systemctl enable mcp-client

# View real-time logs
sudo journalctl -u mcp-client -f
```

## Part 6: Test Your Application

### Step 18: Test the Application

```bash
# Get your instance's public IP
curl http://***************/latest/meta-data/public-ipv4

# Test the health endpoint
curl http://YOUR_PUBLIC_IP:8000/

# Test the servers endpoint
curl http://YOUR_PUBLIC_IP:8000/servers

# Test the chat endpoint
curl -X POST http://YOUR_PUBLIC_IP:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?", "use_tools": false}'
```

### Step 19: Test in Browser

Open your web browser and go to:
```
http://YOUR_INSTANCE_PUBLIC_IP:8000
```

You should see a JSON response like:
```json
{"message": "MCP Client API is running", "status": "healthy"}
```

## Verification Checklist

### ✅ **IAM Role Working:**
```bash
# Should show your role ARN
aws sts get-caller-identity

# Should list Bedrock models
aws bedrock list-foundation-models --region ap-south-1
```

### ✅ **Service Running:**
```bash
# Should show "active (running)"
sudo systemctl status mcp-client
```

### ✅ **Application Accessible:**
```bash
# Should return JSON response
curl http://YOUR_PUBLIC_IP:8000/
```

## Troubleshooting

### **Instance won't connect:**
- Check Security Group allows SSH from your IP
- Verify you're using the correct key file
- Make sure key file permissions are 400

### **Service won't start:**
```bash
# Check detailed logs
sudo journalctl -u mcp-client --no-pager
```

### **Can't access application:**
- Check Security Group allows port 8000
- Verify service is running: `sudo systemctl status mcp-client`
- Check firewall: `sudo iptables -L -n`

### **AWS access denied:**
```bash
# Verify IAM role is attached
curl http://***************/latest/meta-data/iam/security-credentials/
```

## Managing Your Application

### **Start/Stop Service:**
```bash
sudo systemctl start mcp-client    # Start
sudo systemctl stop mcp-client     # Stop
sudo systemctl restart mcp-client  # Restart
sudo systemctl status mcp-client   # Check status
```

### **View Logs:**
```bash
sudo journalctl -u mcp-client -f           # Real-time logs
sudo journalctl -u mcp-client -n 100       # Last 100 lines
sudo journalctl -u mcp-client --since today # Today's logs
```

### **Update Application:**
```bash
# Stop service
sudo systemctl stop mcp-client

# Update your code
sudo nano /opt/mcp-client/main.py

# Restart service
sudo systemctl start mcp-client
```

## Success! 🎉

Your MCP Client is now running at:
**http://YOUR_EC2_PUBLIC_IP:8000**

The application is:
- ✅ **Secure** - Using IAM roles (no credentials in files)
- ✅ **Production-ready** - Proper logging and monitoring
- ✅ **Auto-starting** - Will restart on boot and failures
- ✅ **Accessible** - Available on port 8000

## Next Steps

1. **Test all endpoints** - Try the chat, servers, and tools endpoints
2. **Monitor logs** - Keep an eye on application logs for any issues
3. **Set up monitoring** - Consider CloudWatch for production monitoring
4. **Backup strategy** - Set up regular backups of your application
5. **Domain setup** - Consider setting up a domain name and SSL certificate
