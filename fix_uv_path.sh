#!/bin/bash

# Fix UV Path and MCP Server Integration Script
# This script fixes the UV path issue and gets MCP servers working

set -e

echo "🔧 Fix UV Path and MCP Server Integration Script"
echo "==============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Find correct UV installation
print_status "Step 1: Finding correct UV installation..."

cd /opt/mcp-client

# Find all UV installations
UV_LOCATIONS=$(find /root -name "uv" -type f 2>/dev/null | grep -v cache | grep -v config)
echo "Found UV installations:"
echo "$UV_LOCATIONS"

# Determine the correct UV path
if [[ -f "/root/.local/bin/uv" ]]; then
    UV_PATH="/root/.local/bin/uv"
    print_success "Using UV at: $UV_PATH"
elif [[ -f "/root/.cargo/bin/uv" ]]; then
    UV_PATH="/root/.cargo/bin/uv"
    print_success "Using UV at: $UV_PATH"
else
    print_error "UV not found in expected locations"
    exit 1
fi

# Verify UV works
if $UV_PATH --version &> /dev/null; then
    print_success "UV is functional: $($UV_PATH --version)"
else
    print_error "UV is not working properly"
    exit 1
fi

# Step 2: Update MCP server configuration
print_status "Step 2: Updating MCP server configuration with correct UV path..."

# Update server_config.json with correct UV path and executable name
sudo tee /opt/mcp-client/server_config.json > /dev/null <<EOF
{
  "mcpServers": {
    "pricing": {
      "command": "$UV_PATH",
      "args": [
        "tool",
        "run",
        "--from",
        "awslabs-aws-pricing-mcp-server@latest",
        "awslabs.aws-pricing-mcp-server"
      ],
      "env": {
        "AWS_REGION": "ap-south-1"
      }
    },
    "cost-explorer": {
      "command": "$UV_PATH",
      "args": [
        "tool",
        "run",
        "--from",
        "awslabs-cost-explorer-mcp-server@latest",
        "awslabs.cost-explorer-mcp-server"
      ],
      "env": {
        "AWS_REGION": "ap-south-1"
      }
    },
    "cloudformation": {
      "command": "$UV_PATH",
      "args": [
        "tool",
        "run",
        "--from",
        "awslabs-cfn-mcp-server@latest",
        "awslabs.cfn-mcp-server"
      ],
      "env": {
        "AWS_REGION": "ap-south-1"
      }
    }
  }
}
EOF

print_success "Updated server_config.json with correct UV path"

# Step 3: Create system-wide UV symlink
print_status "Step 3: Creating system-wide UV symlink..."

# Remove any existing broken symlinks
sudo rm -f /usr/local/bin/uv

# Create correct symlink
sudo ln -sf "$UV_PATH" /usr/local/bin/uv

# Verify symlink works
if /usr/local/bin/uv --version &> /dev/null; then
    print_success "System-wide UV symlink created successfully"
else
    print_warning "Symlink creation failed, will use full path in service"
fi

# Step 4: Update systemd service configuration
print_status "Step 4: Updating systemd service configuration..."

# Update service file with correct PATH and environment
sudo tee /etc/systemd/system/mcp-client.service > /dev/null <<EOF
[Unit]
Description=MCP Client FastAPI Backend Service
Documentation=https://fastapi.tiangolo.com/
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/mcp-client
Environment=AWS_REGION=ap-south-1
Environment=BEDROCK_MODEL_ID=apac.anthropic.claude-3-7-sonnet-20250219-v1:0
Environment=MCP_CONFIG_PATH=/opt/mcp-client/server_config.json
Environment=PATH=/usr/local/bin:/root/.local/bin:/root/.cargo/bin:/usr/bin:/bin
Environment=HOME=/root
ExecStart=/opt/mcp-client/venv/bin/gunicorn main:app \\
    --bind 0.0.0.0:8000 \\
    --workers 2 \\
    --worker-class uvicorn.workers.UvicornWorker \\
    --timeout 300 \\
    --max-requests 1000 \\
    --max-requests-jitter 100 \\
    --preload \\
    --access-logfile /var/log/mcp-client/access.log \\
    --error-logfile /var/log/mcp-client/error.log \\
    --log-level info
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings (simplified)
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF

print_success "Updated systemd service configuration"

# Step 5: Test MCP server manually
print_status "Step 5: Testing MCP server manually..."

echo "Testing MCP servers installation..."

# Test pricing server
echo "Testing AWS Pricing MCP server..."
if timeout 30 $UV_PATH tool run --from awslabs-aws-pricing-mcp-server@latest awslabs.aws-pricing-mcp-server --help &> /dev/null; then
    print_success "AWS Pricing MCP server can be installed and run"
else
    print_warning "AWS Pricing MCP server test had issues"
fi

# Test cost-explorer server
echo "Testing AWS Cost Explorer MCP server..."
if timeout 30 $UV_PATH tool run --from awslabs-cost-explorer-mcp-server@latest awslabs.cost-explorer-mcp-server --help &> /dev/null; then
    print_success "AWS Cost Explorer MCP server can be installed and run"
else
    print_warning "AWS Cost Explorer MCP server test had issues"
fi

# Test CloudFormation server
echo "Testing AWS CloudFormation MCP server..."
if timeout 30 $UV_PATH tool run --from awslabs-cfn-mcp-server@latest awslabs.cfn-mcp-server --help &> /dev/null; then
    print_success "AWS CloudFormation MCP server can be installed and run"
else
    print_warning "AWS CloudFormation MCP server test had issues"
fi

# Step 6: Restart service
print_status "Step 6: Restarting MCP Client service..."

# Reload systemd configuration
sudo systemctl daemon-reload

# Restart the service
sudo systemctl restart mcp-client

# Wait for service to start
print_status "Waiting for service to start..."
sleep 20

# Check service status
if sudo systemctl is-active --quiet mcp-client; then
    print_success "MCP Client service is running"
else
    print_error "MCP Client service failed to start"
    echo "Service status:"
    sudo systemctl status mcp-client
    echo -e "\nRecent logs:"
    sudo journalctl -u mcp-client -n 20
    exit 1
fi

# Step 7: Test endpoints
print_status "Step 7: Testing MCP endpoints..."

echo "Testing servers endpoint:"
SERVERS_RESPONSE=$(curl -s http://localhost:8000/servers)
echo $SERVERS_RESPONSE

echo -e "\nTesting tools endpoint:"
TOOLS_RESPONSE=$(curl -s http://localhost:8000/tools)
echo $TOOLS_RESPONSE

# Step 8: Check for errors in logs
print_status "Step 8: Checking service logs for errors..."

echo "Recent service logs:"
sudo journalctl -u mcp-client -n 10

echo -e "\nChecking for UV or MCP errors:"
UV_ERRORS=$(sudo journalctl -u mcp-client -n 20 | grep -i "uv\|error" || echo "No UV errors found")
echo "$UV_ERRORS"

# Step 9: Final assessment
print_status "Step 9: Final assessment..."

# Check if servers are working
if echo "$SERVERS_RESPONSE" | grep -q '"status":"connected"'; then
    print_success "✅ MCP servers are now working!"
    
    # Count servers and tools
    if command -v jq &> /dev/null; then
        SERVER_COUNT=$(echo "$SERVERS_RESPONSE" | jq '.servers | length' 2>/dev/null || echo "1")
        TOOL_COUNT=$(echo "$TOOLS_RESPONSE" | jq '.tools | length' 2>/dev/null || echo "0")
        print_success "Found $SERVER_COUNT connected server(s) with $TOOL_COUNT tool(s)"
    fi
    
    echo -e "\n🎉 MCP integration is fully functional!"
    
elif echo "$SERVERS_RESPONSE" | grep -q '"status":"error"'; then
    print_warning "⚠️ MCP servers have errors"
    echo "Server response: $SERVERS_RESPONSE"
    
    # Check for specific UV errors
    if echo "$SERVERS_RESPONSE" | grep -q "No such file or directory"; then
        print_error "UV path is still incorrect"
        echo "Current UV path in config: $UV_PATH"
        echo "Please check if UV is accessible from the service environment"
    fi
    
else
    print_warning "⚠️ Unexpected server response"
    echo "Server response: $SERVERS_RESPONSE"
fi

# Step 10: Test external access
print_status "Step 10: Testing external access..."

PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4 2>/dev/null || echo "")
if [[ -n "$PUBLIC_IP" ]]; then
    echo "Testing external access at: http://$PUBLIC_IP:8000"
    
    # Test external servers endpoint
    EXTERNAL_SERVERS=$(curl -s http://$PUBLIC_IP:8000/servers 2>/dev/null || echo "Failed to connect")
    if [[ "$EXTERNAL_SERVERS" != "Failed to connect" ]]; then
        print_success "External access is working"
    else
        print_warning "External access may have issues (check security group)"
    fi
else
    print_warning "No public IP found - external access not available"
fi

# Final summary
echo -e "\n📋 Summary:"
echo "🌐 Application URL: http://${PUBLIC_IP:-localhost}:8000"
echo "📖 API Documentation: http://${PUBLIC_IP:-localhost}:8000/docs"
echo "🔧 Service management: sudo systemctl {start|stop|restart|status} mcp-client"
echo "📝 View logs: sudo journalctl -u mcp-client -f"

echo -e "\n🎯 Quick test commands:"
echo "curl http://${PUBLIC_IP:-localhost}:8000/servers"
echo "curl http://${PUBLIC_IP:-localhost}:8000/tools"
echo 'curl -X POST http://'"${PUBLIC_IP:-localhost}"':8000/chat -H "Content-Type: application/json" -d '"'"'{"message": "Test MCP tools", "use_tools": true}'"'"

print_success "UV path fix script completed!"
