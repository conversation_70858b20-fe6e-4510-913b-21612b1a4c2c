#!/bin/bash

# Amazon Linux Setup Script for MCP Client
# Run this script on Amazon Linux 2023 or Amazon Linux 2

set -e

echo "Setting up Amazon Linux instance for MCP Client deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Detect Amazon Linux version
if grep -q "Amazon Linux 2023" /etc/os-release; then
    AMAZON_LINUX_VERSION="2023"
    PACKAGE_MANAGER="dnf"
elif grep -q "Amazon Linux 2" /etc/os-release; then
    AMAZON_LINUX_VERSION="2"
    PACKAGE_MANAGER="yum"
else
    log_error "Unsupported Amazon Linux version"
    exit 1
fi

log_info "Detected Amazon Linux $AMAZON_LINUX_VERSION"

# Update system
log_info "Updating system packages..."
sudo $PACKAGE_MANAGER update -y

# Install essential packages
log_info "Installing essential packages..."
if [ "$AMAZON_LINUX_VERSION" = "2023" ]; then
    sudo dnf install -y \
        python3 \
        python3-pip \
        python3-devel \
        git \
        curl \
        wget \
        unzip \
        htop \
        tree \
        vim \
        tar \
        gzip
else
    sudo yum install -y \
        python3 \
        python3-pip \
        python3-devel \
        git \
        curl \
        wget \
        unzip \
        htop \
        tree \
        vim \
        tar \
        gzip
fi

# Install fail2ban (available in EPEL for Amazon Linux 2)
log_info "Installing fail2ban..."
if [ "$AMAZON_LINUX_VERSION" = "2" ]; then
    sudo amazon-linux-extras install epel -y
    sudo yum install -y fail2ban
elif [ "$AMAZON_LINUX_VERSION" = "2023" ]; then
    sudo dnf install -y fail2ban
fi

# Configure firewall (Amazon Linux uses iptables, not ufw)
log_info "Configuring iptables firewall..."
sudo iptables -F
sudo iptables -P INPUT DROP
sudo iptables -P FORWARD DROP
sudo iptables -P OUTPUT ACCEPT
sudo iptables -A INPUT -i lo -j ACCEPT
sudo iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 8000 -j ACCEPT

# Save iptables rules
sudo service iptables save 2>/dev/null || {
    # For Amazon Linux 2023, save rules manually
    sudo mkdir -p /etc/iptables
    sudo iptables-save > /etc/iptables/rules.v4
    
    # Create systemd service to restore rules on boot
    sudo tee /etc/systemd/system/iptables-restore.service > /dev/null <<EOF
[Unit]
Description=Restore iptables rules
Before=network-pre.target
Wants=network-pre.target

[Service]
Type=oneshot
ExecStart=/sbin/iptables-restore /etc/iptables/rules.v4

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl enable iptables-restore
}

# Configure fail2ban
log_info "Configuring fail2ban..."
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Create swap file (recommended for small instances)
log_info "Creating swap file..."
if [ ! -f /swapfile ]; then
    sudo dd if=/dev/zero of=/swapfile bs=1M count=2048
    sudo chmod 600 /swapfile
    sudo mkswap /swapfile
    sudo swapon /swapfile
    echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
fi

# Configure automatic security updates
log_info "Configuring automatic security updates..."
if [ "$AMAZON_LINUX_VERSION" = "2023" ]; then
    sudo dnf install -y dnf-automatic
    sudo systemctl enable dnf-automatic.timer
    sudo systemctl start dnf-automatic.timer
else
    sudo yum install -y yum-cron
    sudo systemctl enable yum-cron
    sudo systemctl start yum-cron
fi

# Set timezone
log_info "Setting timezone to UTC..."
sudo timedatectl set-timezone UTC

# Install Python virtual environment
log_info "Setting up Python environment..."
python3 -m pip install --user --upgrade pip
python3 -m pip install --user virtualenv

log_info "Amazon Linux setup completed successfully!"
echo ""
log_info "Next steps:"
log_info "1. Upload your application files to the server"
log_info "2. Run the amazon-linux-deploy.sh script"
log_info "3. Configure your .env file with AWS credentials"
log_info "4. Start the service with: sudo systemctl start mcp-client"
echo ""
log_warn "Your application will run on port 8000"
log_warn "Make sure your EC2 Security Group allows inbound traffic on port 8000"
