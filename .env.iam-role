# Minimal Environment Configuration for IAM Role Setup
# No AWS credentials needed - EC2 will use IAM role

# AWS Configuration
AWS_REGION=ap-south-1

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# Application Configuration
APP_ENV=production
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# MCP Server Configuration Path
MCP_CONFIG_PATH=/opt/mcp-client/server_config.json
