[Unit]
Description=MCP Client FastAPI Backend Service
After=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/mcp-client
Environment=AWS_REGION=ap-south-1
Environment=BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
ExecStart=/opt/mcp-client/venv/bin/gunicorn main:app --bind 0.0.0.0:8000 --workers 2 --worker-class uvicorn.workers.UvicornWorker --access-logfile /var/log/mcp-client/access.log --error-logfile /var/log/mcp-client/error.log
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
