# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=ap-south-1

# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0

# Application Configuration
APP_ENV=production
LOG_LEVEL=INFO
HOST=127.0.0.1
PORT=8000

# Security Configuration
SECRET_KEY=your_secret_key_here_generate_a_strong_one

# CORS Configuration (adjust for production)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Database Configuration (if needed in future)
# DATABASE_URL=postgresql://user:password@localhost/dbname

# Monitoring and Logging
ENABLE_METRICS=true
LOG_FILE_PATH=/var/log/mcp-client/app.log

# MCP Server Configuration Path
MCP_CONFIG_PATH=/opt/mcp-client/server_config.json
