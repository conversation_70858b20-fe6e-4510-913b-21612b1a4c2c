# MCP Client Deployment Guide for Amazon Linux

This guide provides step-by-step instructions for deploying your MCP Client FastAPI application on Amazon Linux 2 or Amazon Linux 2023.

## Prerequisites

- AWS EC2 instance running Amazon Linux 2 or Amazon Linux 2023
- SSH access to the instance
- Your application files (main.py, server_config.json, requirements.txt)

## Step 1: Launch EC2 Instance

### Recommended Instance Configuration

**Instance Type**: `t3.small` or `t3.medium`
- **vCPUs**: 2
- **Memory**: 2-4 GB RAM
- **Storage**: 20 GB GP3 SSD

**AMI**: Amazon Linux 2023 (recommended) or Amazon Linux 2

### Security Group Configuration

| Type | Protocol | Port Range | Source | Description |
|------|----------|------------|--------|-------------|
| SSH | TCP | 22 | Your IP | SSH access |
| Custom TCP | TCP | 8000 | 0.0.0.0/0 | FastAPI application |

## Step 2: Connect to Your Instance

```bash
# Connect using EC2 Instance Connect (if enabled)
aws ec2-instance-connect send-ssh-public-key \
    --instance-id i-1234567890abcdef0 \
    --availability-zone us-east-1a \
    --instance-os-user ec2-user \
    --ssh-public-key file://~/.ssh/id_rsa.pub

ssh ec2-user@your-instance-public-ip

# OR connect using your key pair
ssh -i your-key.pem ec2-user@your-instance-public-ip
```

## Step 3: Upload Application Files

### Option A: Using SCP (from your local machine)
```bash
# Create deployment package locally
tar -czf mcp-client-amazon-linux.tar.gz \
  main.py \
  server_config.json \
  requirements.txt \
  .env.production \
  amazon-linux-setup.sh \
  amazon-linux-deploy.sh

# Upload to EC2
scp -i your-key.pem mcp-client-amazon-linux.tar.gz ec2-user@your-instance-ip:~/
```

### Option B: Using Git (if your code is in a repository)
```bash
# On the EC2 instance
git clone https://github.com/your-username/your-repo.git
cd your-repo
```

### Option C: Manual file creation (copy-paste content)
```bash
# Create files manually on the instance
nano main.py
# Copy and paste your main.py content

nano server_config.json
# Copy and paste your server_config.json content

nano requirements.txt
# Copy and paste your requirements.txt content
```

## Step 4: Extract Files (if using SCP)

```bash
# On the EC2 instance
tar -xzf mcp-client-amazon-linux.tar.gz
ls -la  # Verify files are extracted
```

## Step 5: Run Initial Setup

```bash
# Make setup script executable
chmod +x amazon-linux-setup.sh

# Run the setup script
sudo ./amazon-linux-setup.sh
```

This script will:
- Update system packages
- Install Python 3 and development tools
- Configure firewall (iptables)
- Install and configure fail2ban
- Set up automatic security updates
- Create swap file
- Configure timezone

## Step 6: Deploy the Application

```bash
# Make deploy script executable
chmod +x amazon-linux-deploy.sh

# Run the deployment script
sudo ./amazon-linux-deploy.sh
```

This script will:
- Create application directory `/opt/mcp-client`
- Set up Python virtual environment
- Install Python dependencies
- Create systemd service
- Configure log rotation
- Set proper file permissions

## Step 7: Configure AWS Access (IAM Role - Recommended)

### **Option A: IAM Role (Recommended - More Secure)**

**No configuration needed!** The application will automatically use the EC2 instance's IAM role.

Make sure your EC2 instance has an IAM role with these permissions:
- `bedrock:InvokeModel`
- `bedrock:InvokeModelWithResponseStream`
- `pricing:GetProducts` (for pricing MCP server)
- `ce:GetCostAndUsage` (for cost explorer MCP server)

See the **`IAM_ROLE_SETUP_GUIDE.md`** for detailed setup instructions.

### **Option B: Environment Variables (Alternative)**

If you prefer to use AWS credentials in environment variables:

```bash
sudo nano /opt/mcp-client/.env
```

Add these variables:
```bash
AWS_ACCESS_KEY_ID=AKIA...your_actual_access_key
AWS_SECRET_ACCESS_KEY=your_actual_secret_key
AWS_REGION=us-east-1
BEDROCK_MODEL_ID=anthropic.claude-3-sonnet-20240229-v1:0
```

**Note**: IAM roles are more secure and recommended for production.

## Step 8: Start the Service

```bash
# Start the MCP Client service
sudo systemctl start mcp-client

# Check if it's running
sudo systemctl status mcp-client

# Enable auto-start on boot
sudo systemctl enable mcp-client

# View real-time logs
sudo journalctl -u mcp-client -f
```

## Step 9: Test Your Application

### Get your instance's public IP
```bash
curl http://***************/latest/meta-data/public-ipv4
```

### Test the application
```bash
# Health check
curl http://YOUR_PUBLIC_IP:8000/

# List servers endpoint
curl http://YOUR_PUBLIC_IP:8000/servers

# List tools endpoint
curl http://YOUR_PUBLIC_IP:8000/tools

# Test chat endpoint
curl -X POST http://YOUR_PUBLIC_IP:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, how are you?", "use_tools": false}'
```

### Test from your browser
Open: `http://YOUR_PUBLIC_IP:8000`

## Service Management Commands

```bash
# Start service
sudo systemctl start mcp-client

# Stop service
sudo systemctl stop mcp-client

# Restart service
sudo systemctl restart mcp-client

# Check status
sudo systemctl status mcp-client

# View logs (real-time)
sudo journalctl -u mcp-client -f

# View logs (last 100 lines)
sudo journalctl -u mcp-client -n 100

# View logs since today
sudo journalctl -u mcp-client --since today
```

## Monitoring and Maintenance

### Check System Resources
```bash
# CPU and memory usage
htop

# Disk usage
df -h

# Memory usage
free -h

# Check if port 8000 is listening
sudo netstat -tlnp | grep :8000

# Check active connections
sudo netstat -an | grep :8000
```

### Application Logs
```bash
# Application logs via journalctl
sudo journalctl -u mcp-client -f

# Check for errors
sudo journalctl -u mcp-client | grep -i error

# Check for warnings
sudo journalctl -u mcp-client | grep -i warn
```

## Troubleshooting

### Common Issues

1. **Service fails to start**:
   ```bash
   # Check detailed error logs
   sudo journalctl -u mcp-client --no-pager
   
   # Check if Python dependencies are installed
   /opt/mcp-client/venv/bin/pip list
   
   # Test running manually
   cd /opt/mcp-client
   ./venv/bin/python main.py
   ```

2. **Port 8000 not accessible**:
   ```bash
   # Check if service is running
   sudo systemctl status mcp-client
   
   # Check firewall rules
   sudo iptables -L -n
   
   # Check Security Group in AWS Console
   # Make sure port 8000 is allowed from 0.0.0.0/0
   ```

3. **AWS credentials error**:
   ```bash
   # Verify credentials file
   sudo cat /opt/mcp-client/.env
   
   # Test AWS credentials
   aws sts get-caller-identity
   
   # Check if AWS CLI is configured
   aws configure list
   ```

4. **Python import errors**:
   ```bash
   # Reinstall dependencies
   cd /opt/mcp-client
   sudo ./venv/bin/pip install -r requirements.txt --force-reinstall
   
   # Check Python path
   sudo ./venv/bin/python -c "import sys; print(sys.path)"
   ```

### Performance Tuning

1. **Increase workers** (for higher traffic):
   ```bash
   sudo nano /etc/systemd/system/mcp-client.service
   # Change: --workers 4 to --workers 8
   sudo systemctl daemon-reload
   sudo systemctl restart mcp-client
   ```

2. **Monitor resource usage**:
   ```bash
   # Real-time monitoring
   htop
   
   # Check memory usage over time
   free -h -s 5
   ```

## Updating Your Application

```bash
# Stop the service
sudo systemctl stop mcp-client

# Update your code (example with git)
cd /path/to/your/code
git pull origin main

# Copy updated files
sudo cp main.py /opt/mcp-client/
sudo cp server_config.json /opt/mcp-client/

# Update dependencies if needed
sudo /opt/mcp-client/venv/bin/pip install -r requirements.txt

# Restart the service
sudo systemctl start mcp-client

# Check if it's running properly
sudo systemctl status mcp-client
```

## Security Considerations

### Firewall Status
```bash
# Check iptables rules
sudo iptables -L -n

# Check fail2ban status
sudo systemctl status fail2ban
sudo fail2ban-client status
```

### File Permissions
```bash
# Verify secure permissions
ls -la /opt/mcp-client/.env  # Should be 600 (rw-------)
ls -la /opt/mcp-client/      # Should be 755 for directories
```

## Backup

### Simple Backup
```bash
# Create backup
sudo tar -czf /tmp/mcp-client-backup-$(date +%Y%m%d).tar.gz /opt/mcp-client

# Copy to S3 (optional)
aws s3 cp /tmp/mcp-client-backup-$(date +%Y%m%d).tar.gz s3://your-backup-bucket/
```

Your MCP Client FastAPI application should now be running successfully on Amazon Linux at:
**http://YOUR_EC2_PUBLIC_IP:8000**
