#!/bin/bash

# MCP Client Deployment Script for AWS EC2
# Run this script as root or with sudo privileges

set -e  # Exit on any error

echo "Starting MCP Client deployment..."

# Configuration
APP_NAME="mcp-client"
APP_USER="mcpuser"
APP_DIR="/opt/${APP_NAME}"
LOG_DIR="/var/log/${APP_NAME}"
SERVICE_FILE="${APP_NAME}.service"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Update system packages
log_info "Updating system packages..."
apt update && apt upgrade -y

# Install required system packages
log_info "Installing system dependencies..."
apt install -y python3 python3-pip python3-venv nginx ufw fail2ban htop curl wget git unzip

# Install UV (fast Python package installer)
log_info "Installing UV package manager..."
curl -LsSf https://astral.sh/uv/install.sh | sh
export PATH="$HOME/.cargo/bin:$PATH"

# Create application user
log_info "Creating application user: ${APP_USER}"
if ! id "${APP_USER}" &>/dev/null; then
    useradd --system --shell /bin/bash --home-dir ${APP_DIR} --create-home ${APP_USER}
else
    log_warn "User ${APP_USER} already exists"
fi

# Create application directory
log_info "Setting up application directory: ${APP_DIR}"
mkdir -p ${APP_DIR}
chown ${APP_USER}:${APP_USER} ${APP_DIR}

# Create log directory
log_info "Creating log directory: ${LOG_DIR}"
mkdir -p ${LOG_DIR}
chown ${APP_USER}:${APP_USER} ${LOG_DIR}

# Copy application files
log_info "Copying application files..."
cp main.py ${APP_DIR}/
cp server_config.json ${APP_DIR}/
cp requirements.txt ${APP_DIR}/
cp .env.production ${APP_DIR}/.env
chown -R ${APP_USER}:${APP_USER} ${APP_DIR}

# Setup Python virtual environment
log_info "Setting up Python virtual environment..."
sudo -u ${APP_USER} python3 -m venv ${APP_DIR}/venv
sudo -u ${APP_USER} ${APP_DIR}/venv/bin/pip install --upgrade pip

# Install Python dependencies
log_info "Installing Python dependencies..."
sudo -u ${APP_USER} ${APP_DIR}/venv/bin/pip install -r ${APP_DIR}/requirements.txt

# Install systemd service
log_info "Installing systemd service..."
cp ${SERVICE_FILE} /etc/systemd/system/
systemctl daemon-reload
systemctl enable ${SERVICE_FILE}

log_info "Deployment completed successfully!"
log_warn "Please update the .env file with your actual AWS credentials before starting the service"
log_info "To start the service: sudo systemctl start ${APP_NAME}"
log_info "To check status: sudo systemctl status ${APP_NAME}"
log_info "To view logs: sudo journalctl -u ${APP_NAME} -f"
