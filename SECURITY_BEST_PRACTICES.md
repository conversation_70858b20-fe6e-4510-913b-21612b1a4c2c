# Security Best Practices for MCP Client Deployment

This document outlines essential security practices for deploying the MCP Client FastAPI application on AWS EC2.

## 1. AWS Security Configuration

### IAM Best Practices

#### Create Dedicated IAM User
```bash
# Create IAM user for the application
aws iam create-user --user-name mcp-client-app

# Create access key
aws iam create-access-key --user-name mcp-client-app
```

#### Minimal IAM Policy
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": [
                "arn:aws:bedrock:*:*:foundation-model/anthropic.claude-*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "pricing:GetProducts",
                "pricing:DescribeServices"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "ce:GetCostAndUsage",
                "ce:GetUsageReport"
            ],
            "Resource": "*"
        }
    ]
}
```

### EC2 Security Groups

#### Restrictive Security Group Rules
```bash
# Create security group
aws ec2 create-security-group \
    --group-name mcp-client-sg \
    --description "Security group for MCP Client application"

# SSH access (restrict to your IP)
aws ec2 authorize-security-group-ingress \
    --group-id sg-xxxxxxxxx \
    --protocol tcp \
    --port 22 \
    --cidr YOUR_IP/32

# HTTPS only (no HTTP in production)
aws ec2 authorize-security-group-ingress \
    --group-id sg-xxxxxxxxx \
    --protocol tcp \
    --port 443 \
    --cidr 0.0.0.0/0
```

## 2. Server Hardening

### System Security Updates
```bash
# Enable automatic security updates
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades

# Configure automatic updates
sudo tee /etc/apt/apt.conf.d/20auto-upgrades > /dev/null <<EOF
APT::Periodic::Update-Package-Lists "1";
APT::Periodic::Unattended-Upgrade "1";
APT::Periodic::AutocleanInterval "7";
EOF
```

### SSH Hardening
```bash
# Edit SSH configuration
sudo nano /etc/ssh/sshd_config

# Recommended settings:
# PermitRootLogin no
# PasswordAuthentication no
# PubkeyAuthentication yes
# Port 2222  # Change default port
# MaxAuthTries 3
# ClientAliveInterval 300
# ClientAliveCountMax 2

# Restart SSH service
sudo systemctl restart sshd
```

### Firewall Configuration
```bash
# Configure UFW with strict rules
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH (use custom port if changed)
sudo ufw allow 2222/tcp

# Allow HTTPS only
sudo ufw allow 443/tcp

# Enable firewall
sudo ufw enable

# Check status
sudo ufw status verbose
```

### Fail2Ban Configuration
```bash
# Configure fail2ban for SSH protection
sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = 2222
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
EOF

sudo systemctl restart fail2ban
```

## 3. Application Security

### Environment Variables Security
```bash
# Secure .env file permissions
sudo chmod 600 /opt/mcp-client/.env
sudo chown mcpuser:mcpuser /opt/mcp-client/.env

# Verify no secrets in logs
sudo grep -r "AWS_SECRET" /var/log/ || echo "No secrets found in logs"
```

### Application User Security
```bash
# Create restricted user
sudo useradd --system --shell /bin/false --home-dir /opt/mcp-client mcpuser

# Set proper permissions
sudo chown -R mcpuser:mcpuser /opt/mcp-client
sudo chmod -R 750 /opt/mcp-client
sudo chmod 640 /opt/mcp-client/.env
```

### Secrets Management
```bash
# Use AWS Secrets Manager (recommended)
aws secretsmanager create-secret \
    --name "mcp-client/prod/credentials" \
    --description "MCP Client production credentials" \
    --secret-string '{"AWS_ACCESS_KEY_ID":"xxx","AWS_SECRET_ACCESS_KEY":"xxx"}'

# Update application to use Secrets Manager
# Add to requirements.txt: boto3
```

## 4. Network Security

### SSL/TLS Configuration
```bash
# Strong SSL configuration in Nginx
sudo tee -a /etc/nginx/sites-available/mcp-client > /dev/null <<EOF
# SSL Security
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
ssl_prefer_server_ciphers off;
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
ssl_stapling on;
ssl_stapling_verify on;

# Security headers
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
add_header X-Frame-Options DENY always;
add_header X-Content-Type-Options nosniff always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
EOF
```

### Rate Limiting
```bash
# Configure rate limiting in Nginx
sudo tee /etc/nginx/conf.d/rate-limiting.conf > /dev/null <<EOF
# Rate limiting zones
limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone \$binary_remote_addr zone=chat:10m rate=5r/s;

# Apply to specific endpoints
location /chat {
    limit_req zone=chat burst=10 nodelay;
    proxy_pass http://mcp_client;
}

location /tools/call {
    limit_req zone=api burst=5 nodelay;
    proxy_pass http://mcp_client;
}
EOF
```

## 5. Monitoring and Logging

### Security Monitoring
```bash
# Install and configure auditd
sudo apt install auditd audispd-plugins

# Configure audit rules
sudo tee -a /etc/audit/rules.d/audit.rules > /dev/null <<EOF
# Monitor file access
-w /opt/mcp-client/.env -p wa -k env-access
-w /opt/mcp-client/main.py -p wa -k app-modification
-w /etc/systemd/system/mcp-client.service -p wa -k service-modification

# Monitor network connections
-a always,exit -F arch=b64 -S connect -k network-connect
EOF

sudo systemctl restart auditd
```

### Log Security
```bash
# Secure log files
sudo chmod 640 /var/log/mcp-client/*.log
sudo chown mcpuser:adm /var/log/mcp-client/*.log

# Configure log rotation with compression
sudo tee /etc/logrotate.d/mcp-client > /dev/null <<EOF
/var/log/mcp-client/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 640 mcpuser adm
    postrotate
        systemctl reload mcp-client
    endscript
}
EOF
```

## 6. Backup and Recovery Security

### Encrypted Backups
```bash
# Create encrypted backup script
sudo tee /opt/backup-secure.sh > /dev/null <<EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="/tmp/mcp-client-\$DATE.tar.gz"
ENCRYPTED_FILE="/opt/backups/mcp-client-\$DATE.tar.gz.gpg"

# Create backup
tar -czf \$BACKUP_FILE /opt/mcp-client --exclude=/opt/mcp-client/.env

# Encrypt backup
gpg --symmetric --cipher-algo AES256 --output \$ENCRYPTED_FILE \$BACKUP_FILE

# Upload to S3
aws s3 cp \$ENCRYPTED_FILE s3://your-secure-backup-bucket/

# Cleanup
rm \$BACKUP_FILE \$ENCRYPTED_FILE
EOF

chmod +x /opt/backup-secure.sh
```

## 7. Compliance and Auditing

### Security Checklist
- [ ] IAM user with minimal permissions
- [ ] Security groups with restrictive rules
- [ ] SSH key-based authentication only
- [ ] Custom SSH port
- [ ] UFW firewall enabled
- [ ] Fail2ban configured
- [ ] SSL/TLS with strong ciphers
- [ ] Security headers configured
- [ ] Rate limiting implemented
- [ ] Audit logging enabled
- [ ] Encrypted backups
- [ ] Regular security updates
- [ ] Log monitoring
- [ ] Secrets properly managed

### Regular Security Tasks
```bash
# Weekly security check script
sudo tee /opt/security-check.sh > /dev/null <<EOF
#!/bin/bash
echo "=== Security Check Report ===" > /tmp/security-report.txt
echo "Date: \$(date)" >> /tmp/security-report.txt

# Check for failed login attempts
echo "Failed SSH attempts:" >> /tmp/security-report.txt
grep "Failed password" /var/log/auth.log | tail -10 >> /tmp/security-report.txt

# Check UFW status
echo "Firewall status:" >> /tmp/security-report.txt
ufw status >> /tmp/security-report.txt

# Check for security updates
echo "Available security updates:" >> /tmp/security-report.txt
apt list --upgradable 2>/dev/null | grep -i security >> /tmp/security-report.txt

# Email report (configure mail server)
# mail -s "Security Report" <EMAIL> < /tmp/security-report.txt
EOF

chmod +x /opt/security-check.sh
```

## 8. Incident Response

### Emergency Procedures
1. **Suspected Breach**:
   ```bash
   # Immediately stop the service
   sudo systemctl stop mcp-client
   
   # Block all traffic
   sudo ufw deny incoming
   
   # Check for unauthorized access
   sudo last -n 20
   sudo grep "Accepted" /var/log/auth.log
   ```

2. **Recovery Steps**:
   ```bash
   # Rotate credentials
   aws iam create-access-key --user-name mcp-client-app
   aws iam delete-access-key --user-name mcp-client-app --access-key-id OLD_KEY
   
   # Update application credentials
   sudo nano /opt/mcp-client/.env
   
   # Restart service
   sudo systemctl start mcp-client
   ```

Remember to regularly review and update these security measures based on the latest threats and AWS security recommendations.
