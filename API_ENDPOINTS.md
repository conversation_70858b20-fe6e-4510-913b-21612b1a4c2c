# MCP Client API Endpoints Documentation

## 🌐 Base URL
```
http://************:8000
```

## 📋 API Endpoints Overview

Your FastAPI application provides **9 endpoints** across different categories with **3 MCP servers** and **24 tools** available.

---

## 🏥 Health & Status

### GET `/`
**Health check endpoint**
- **URL**: `http://************:8000/`
- **Method**: GET
- **Description**: Returns API status and health information
- **Response**:
```json
{
  "message": "MCP Client API is running",
  "status": "healthy"
}
```

---

## 🖥️ Server Management

### GET `/servers`
**List all MCP servers**
- **URL**: `http://************:8000/servers`
- **Method**: GET
- **Description**: Shows status, tools count, and configuration for all servers
- **Response**:
```json
{
  "servers": [
    {
      "name": "pricing",
      "status": "connected",
      "tools_count": 9,
      "error": null
    },
    {
      "name": "cost-explorer",
      "status": "connected",
      "tools_count": 7,
      "error": null
    },
    {
      "name": "cloudformation",
      "status": "connected",
      "tools_count": 8,
      "error": null
    }
  ]
}
```

### POST `/servers`
**Add a new MCP server**
- **URL**: `http://************:8000/servers`
- **Method**: POST
- **Description**: Dynamically add new MCP server configurations
- **Request Body**:
```json
{
  "name": "server_name",
  "command": "uv",
  "args": ["tool", "run", "--from", "package@latest", "executable"],
  "env": {
    "AWS_REGION": "ap-south-1"
  }
}
```

### GET `/servers/{server_name}`
**Get specific server details**
- **URL**: `http://************:8000/servers/{server_name}`
- **Method**: GET
- **Description**: Detailed information about a particular MCP server
- **Example**: `http://************:8000/servers/pricing`

### POST `/servers/{server_name}/reconnect`
**Reconnect to a server**
- **URL**: `http://************:8000/servers/{server_name}/reconnect`
- **Method**: POST
- **Description**: Manually reconnect to a specific MCP server
- **Example**: `http://************:8000/servers/pricing/reconnect`

---

## 🔧 Tools Management

### GET `/tools`
**List all available tools**
- **URL**: `http://************:8000/tools`
- **Method**: GET
- **Description**: Shows all 24 tools across all 3 MCP servers
- **Response**: Returns tools with their descriptions and schemas

### POST `/tools/call`
**Call a specific tool**
- **URL**: `http://************:8000/tools/call`
- **Method**: POST
- **Description**: Direct tool execution with parameters
- **Request Body**:
```json
{
  "server_name": "pricing",
  "tool_name": "get_pricing",
  "arguments": {
    "service": "EC2",
    "region": "ap-south-1"
  }
}
```

---

## 💬 Chat Interface

### POST `/chat`
**Main chat endpoint**
- **URL**: `http://************:8000/chat`
- **Method**: POST
- **Description**: AI-powered chat with optional tool usage
- **Request Body**:
```json
{
  "message": "What is AWS Lambda pricing?",
  "use_tools": true,
  "conversation_id": "optional-conversation-id"
}
```
- **Response**:
```json
{
  "response": "AI response text with pricing information",
  "conversation_id": "conv_12345678",
  "tools_used": ["get_pricing"],
  "status": "success"
}
```

---

## 🐛 Debug & Monitoring

### GET `/debug/connections`
**Debug connection states**
- **URL**: `http://************:8000/debug/connections`
- **Method**: GET
- **Description**: Internal debugging information for MCP connections
- **Response**: Shows connection status, tools count, and session information

---

## 🧪 Frontend Testing Examples

### JavaScript/Fetch Examples

```javascript
// Health Check
fetch('http://************:8000/')
  .then(response => response.json())
  .then(data => console.log(data));

// Get All Servers
fetch('http://************:8000/servers')
  .then(response => response.json())
  .then(data => console.log(data));

// Get All Tools
fetch('http://************:8000/tools')
  .then(response => response.json())
  .then(data => console.log(data));

// Chat with AI
fetch('http://************:8000/chat', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    message: "What is AWS Lambda pricing?",
    use_tools: true
  })
})
.then(response => response.json())
.then(data => console.log(data));

// Get Specific Server Details
fetch('http://************:8000/servers/pricing')
  .then(response => response.json())
  .then(data => console.log(data));
```

### cURL Commands

```bash
# Health Check
curl http://************:8000/

# List Servers
curl http://************:8000/servers

# List Tools
curl http://************:8000/tools

# Chat Request
curl -X POST http://************:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Show me AWS EC2 pricing", "use_tools": true}'

# Get Server Details
curl http://************:8000/servers/cost-explorer

# Debug Connections
curl http://************:8000/debug/connections
```

---

## 📖 Interactive Documentation

### Swagger UI (Recommended for Frontend Developers)
```
http://************:8000/docs
```

### ReDoc (Alternative Documentation)
```
http://************:8000/redoc
```

---

## 🎯 Key Features

- ✅ **9 total endpoints**
- ✅ **3 MCP servers** (pricing, cost-explorer, cloudformation)
- ✅ **24 tools** available across all servers
- ✅ **RESTful design** with proper HTTP methods
- ✅ **CORS enabled** for web frontends
- ✅ **Interactive documentation** available
- ✅ **Debug capabilities** for monitoring
- ✅ **Dynamic server management** (add/reconnect servers)

---

## 🔧 CORS Configuration

Your API has CORS enabled for all origins:
- ✅ **All origins allowed**
- ✅ **All methods allowed** (GET, POST, PUT, DELETE)
- ✅ **All headers allowed**

---

## 🚀 Available MCP Servers

| Server | Tools | Capabilities |
|--------|-------|-------------|
| **pricing** | 9 tools | AWS service pricing, cost estimation |
| **cost-explorer** | 7 tools | Actual spending analysis, cost trends |
| **cloudformation** | 8 tools | Infrastructure management, stack operations |

---

## 📞 Support

For testing and integration support, use the interactive documentation at:
`http://************:8000/docs`
