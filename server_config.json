{"mcpServers": {"cost-explorer": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-cost-explorer-mcp-server@latest", "awslabs-cost-explorer-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1"}}, "cloudformation": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-cfn-mcp-server@latest", "awslabs-cfn-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1"}}, "pricing": {"command": "uv", "args": ["tool", "run", "--from", "awslabs-aws-pricing-mcp-server@latest", "awslabs-aws-pricing-mcp-server"], "env": {"AWS_PROFILE": "default", "AWS_REGION": "ap-south-1"}}}}