#!/bin/bash

# Comprehensive MCP Server Diagnostic and Fix Script
# This script diagnoses and fixes all MCP server integration issues

set -e

echo "🔍 Comprehensive MCP Server Diagnostic and Fix Script"
echo "====================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

print_fix() {
    echo -e "${CYAN}[FIX]${NC} $1"
}

# Step 1: Examine MCP server integration in main.py
print_status "Step 1: Examining MCP server integration in main.py..."

cd /opt/mcp-client

# Check if main.py exists
if [[ ! -f "main.py" ]]; then
    print_error "main.py not found!"
    exit 1
fi

# Analyze MCP-related code in main.py
print_debug "Checking for MCP imports..."
MCP_IMPORTS=$(grep -n "import.*mcp\|from.*mcp" main.py || echo "")
if [[ -n "$MCP_IMPORTS" ]]; then
    print_success "Found MCP imports:"
    echo "$MCP_IMPORTS"
else
    print_warning "No MCP imports found in main.py"
fi

print_debug "Checking for MCP server configuration loading..."
CONFIG_LOADING=$(grep -n "server_config\|mcpServers" main.py || echo "")
if [[ -n "$CONFIG_LOADING" ]]; then
    print_success "Found server config references:"
    echo "$CONFIG_LOADING"
else
    print_warning "No server config loading found in main.py"
fi

print_debug "Checking /servers endpoint implementation..."
SERVERS_ENDPOINT=$(grep -A 20 "@app.get(\"/servers\")" main.py || echo "")
if [[ -n "$SERVERS_ENDPOINT" ]]; then
    print_success "Found /servers endpoint:"
    echo "$SERVERS_ENDPOINT"
else
    print_warning "No /servers endpoint found in main.py"
fi

print_debug "Checking /tools endpoint implementation..."
TOOLS_ENDPOINT=$(grep -A 20 "@app.get(\"/tools\")" main.py || echo "")
if [[ -n "$TOOLS_ENDPOINT" ]]; then
    print_success "Found /tools endpoint:"
    echo "$TOOLS_ENDPOINT"
else
    print_warning "No /tools endpoint found in main.py"
fi

# Step 2: Check current API responses
print_status "Step 2: Testing current API responses..."

echo "Health check:"
curl -s http://localhost:8000/ | jq '.' 2>/dev/null || curl -s http://localhost:8000/

echo -e "\nServers endpoint:"
SERVERS_RESPONSE=$(curl -s http://localhost:8000/servers)
echo $SERVERS_RESPONSE

echo -e "\nTools endpoint:"
TOOLS_RESPONSE=$(curl -s http://localhost:8000/tools)
echo $TOOLS_RESPONSE

# Analyze responses
if [[ "$SERVERS_RESPONSE" == "{}" ]]; then
    print_warning "Servers endpoint returns empty object - MCP integration missing or broken"
    MCP_INTEGRATION_NEEDED=true
else
    print_success "Servers endpoint returns data"
    MCP_INTEGRATION_NEEDED=false
fi

if [[ "$TOOLS_RESPONSE" == "{}" ]]; then
    print_warning "Tools endpoint returns empty object - MCP integration missing or broken"
else
    print_success "Tools endpoint returns data"
fi

# Step 3: Verify MCP server configuration loading
print_status "Step 3: Verifying MCP server configuration loading..."

if [[ -f "server_config.json" ]]; then
    print_success "server_config.json exists"

    # Validate JSON syntax
    if python3 -m json.tool server_config.json > /dev/null 2>&1; then
        print_success "server_config.json is valid JSON"

        # Show config content
        print_debug "Configuration content:"
        cat server_config.json | jq '.' 2>/dev/null || cat server_config.json

        # Check if config has servers
        SERVER_COUNT=$(python3 -c "import json; config=json.load(open('server_config.json')); print(len(config.get('mcpServers', {})))")
        if [[ "$SERVER_COUNT" -gt 0 ]]; then
            print_success "Found $SERVER_COUNT MCP server(s) in configuration"
        else
            print_warning "No MCP servers found in configuration"
        fi
    else
        print_error "server_config.json is invalid JSON"
        exit 1
    fi
else
    print_error "server_config.json not found"
    exit 1
fi

# Step 4: Check UV installation and accessibility
print_status "Step 4: Checking UV tool installation and accessibility..."

# Check if UV is in PATH
if command -v uv &> /dev/null; then
    print_success "UV is accessible: $(uv --version)"
    UV_ACCESSIBLE=true
else
    print_warning "UV not found in current PATH"
    UV_ACCESSIBLE=false

    # Look for UV in common locations
    UV_LOCATIONS=(
        "/root/.cargo/bin/uv"
        "/usr/local/bin/uv"
        "/usr/bin/uv"
        "$HOME/.cargo/bin/uv"
    )

    for location in "${UV_LOCATIONS[@]}"; do
        if [[ -f "$location" ]]; then
            print_success "Found UV at: $location"
            export PATH="$(dirname $location):$PATH"
            UV_ACCESSIBLE=true
            break
        fi
    done

    if [[ "$UV_ACCESSIBLE" == "false" ]]; then
        print_warning "UV not found - installing..."

        # Install UV
        print_status "Installing UV tool..."
        curl -LsSf https://astral.sh/uv/install.sh | sh

        # Add to PATH
        export PATH="$HOME/.cargo/bin:$PATH"
        echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.bashrc

        # Make UV available system-wide
        if [ -f "$HOME/.cargo/bin/uv" ]; then
            sudo cp "$HOME/.cargo/bin/uv" /usr/local/bin/
            sudo chmod +x /usr/local/bin/uv
            print_success "UV installed successfully"
            UV_ACCESSIBLE=true
        else
            print_error "UV installation failed"
            exit 1
        fi
    fi
fi

# Step 5: Test MCP client functionality
print_status "Step 5: Testing MCP client functionality..."

# Test UV functionality
if uv --version &> /dev/null; then
    print_success "UV is working: $(uv --version)"
else
    print_error "UV is not working properly"
    exit 1
fi

# Test MCP server installation
print_debug "Testing AWS pricing MCP server installation..."
if timeout 30 uv tool run --from awslabs-aws-pricing-mcp-server@latest awslabs-aws-pricing-mcp-server --help &> /dev/null; then
    print_success "AWS pricing MCP server can be installed and run"
else
    print_warning "AWS pricing MCP server installation may have issues"
fi

# Test Python MCP library
print_debug "Testing Python MCP library..."
if ./venv/bin/python -c "import mcp; from mcp import ClientSession, StdioServerParameters; from mcp.client.stdio import stdio_client; print('MCP library working')" 2>/dev/null; then
    print_success "Python MCP library is functional"
else
    print_warning "Python MCP library has issues - reinstalling..."
    ./venv/bin/pip install mcp --upgrade
fi

# Step 5: Check Python MCP library
print_status "Step 5: Checking Python MCP library..."

cd /opt/mcp-client
if sudo ./venv/bin/python -c "import mcp; print(f'MCP version: {mcp.__version__}')" 2>/dev/null; then
    print_success "MCP Python library is working"
else
    print_warning "MCP Python library has issues - reinstalling..."
    sudo ./venv/bin/pip install mcp --upgrade
fi

# Step 6: Update service configuration
print_status "Step 6: Updating service configuration..."

# Backup current service file
sudo cp /etc/systemd/system/mcp-client.service /etc/systemd/system/mcp-client.service.backup

# Update service file to include proper PATH
sudo tee /etc/systemd/system/mcp-client.service > /dev/null <<'EOF'
[Unit]
Description=MCP Client FastAPI Backend Service
Documentation=https://fastapi.tiangolo.com/
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/mcp-client
Environment=AWS_REGION=ap-south-1
Environment=BEDROCK_MODEL_ID=apac.anthropic.claude-3-7-sonnet-20250219-v1:0
Environment=PATH=/usr/local/bin:/usr/bin:/bin:/root/.cargo/bin
ExecStart=/opt/mcp-client/venv/bin/gunicorn main:app \
    --bind 0.0.0.0:8000 \
    --workers 2 \
    --worker-class uvicorn.workers.UvicornWorker \
    --timeout 300 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --preload \
    --access-logfile /var/log/mcp-client/access.log \
    --error-logfile /var/log/mcp-client/error.log \
    --log-level info
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings (simplified)
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF

print_success "Service configuration updated with proper PATH"

# Step 7: Create simplified MCP config for testing
print_status "Step 7: Creating simplified MCP configuration..."

sudo tee /opt/mcp-client/server_config_test.json > /dev/null <<'EOF'
{
  "mcpServers": {
    "pricing": {
      "command": "uv",
      "args": [
        "tool",
        "run",
        "--from",
        "awslabs-aws-pricing-mcp-server@latest",
        "awslabs-aws-pricing-mcp-server"
      ],
      "env": {
        "AWS_REGION": "ap-south-1"
      }
    }
  }
}
EOF

# Backup original config and use test config
sudo cp /opt/mcp-client/server_config.json /opt/mcp-client/server_config.json.backup
sudo cp /opt/mcp-client/server_config_test.json /opt/mcp-client/server_config.json

print_success "Simplified MCP configuration created"

# Step 8: Restart service
print_status "Step 8: Restarting MCP Client service..."

sudo systemctl daemon-reload
sudo systemctl restart mcp-client

# Wait for service to start
sleep 10

# Check service status
if sudo systemctl is-active --quiet mcp-client; then
    print_success "MCP Client service is running"
else
    print_error "MCP Client service failed to start"
    echo "Service status:"
    sudo systemctl status mcp-client
    echo -e "\nRecent logs:"
    sudo journalctl -u mcp-client -n 20
    exit 1
fi

# Step 9: Test endpoints again
print_status "Step 9: Testing endpoints after fixes..."

sleep 5

echo "Testing servers endpoint:"
SERVERS_RESPONSE=$(curl -s http://localhost:8000/servers)
echo $SERVERS_RESPONSE

echo -e "\nTesting tools endpoint:"
TOOLS_RESPONSE=$(curl -s http://localhost:8000/tools)
echo $TOOLS_RESPONSE

# Step 10: Test chat with tools
print_status "Step 10: Testing chat with tools..."

echo "Testing chat endpoint with tools:"
curl -s -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "What is the pricing for AWS Lambda?", "use_tools": true}' | \
  jq '.tools_used' 2>/dev/null || echo "Tools used info not available"

# Step 11: Check logs for errors
print_status "Step 11: Checking recent logs for errors..."

echo "Recent service logs:"
sudo journalctl -u mcp-client -n 10

echo -e "\nError log:"
sudo tail -n 10 /var/log/mcp-client/error.log 2>/dev/null || echo "No error log found"

# Step 12: Final status
print_status "Step 12: Final status check..."

if [[ "$SERVERS_RESPONSE" != "{}" ]]; then
    print_success "✅ MCP servers are now working!"
    echo "🌐 Your API is available at: http://************:8000"
    echo "📖 API Documentation: http://************:8000/docs"
else
    print_warning "⚠️ MCP servers still not working"
    echo "Manual debugging needed. Check logs:"
    echo "sudo journalctl -u mcp-client -f"
fi

echo -e "\n🎯 Quick test commands:"
echo "curl http://************:8000/servers"
echo "curl http://************:8000/tools"
echo 'curl -X POST http://************:8000/chat -H "Content-Type: application/json" -d '"'"'{"message": "Test tools", "use_tools": true}'"'"

print_success "Script completed!"
