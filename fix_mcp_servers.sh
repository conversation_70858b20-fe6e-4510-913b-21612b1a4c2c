#!/bin/bash

# MCP Server Diagnostic and Fix Script
# This script diagnoses and fixes MCP server issues

set -e

echo "🔍 MCP Server Diagnostic and Fix Script"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Check current status
print_status "Step 1: Checking current MCP server status..."

echo "Testing current endpoints:"
echo "Health check:"
curl -s http://localhost:8000/ | jq '.' 2>/dev/null || curl -s http://localhost:8000/

echo -e "\nServers endpoint:"
SERVERS_RESPONSE=$(curl -s http://localhost:8000/servers)
echo $SERVERS_RESPONSE

echo -e "\nTools endpoint:"
TOOLS_RESPONSE=$(curl -s http://localhost:8000/tools)
echo $TOOLS_RESPONSE

if [[ "$SERVERS_RESPONSE" == "{}" ]]; then
    print_warning "MCP servers are not working - proceeding with fixes"
else
    print_success "MCP servers appear to be working"
    exit 0
fi

# Step 2: Check UV installation
print_status "Step 2: Checking UV tool installation..."

if command -v uv &> /dev/null; then
    print_success "UV is installed: $(uv --version)"
else
    print_warning "UV not found - installing..."
    
    # Install UV
    print_status "Installing UV tool..."
    curl -LsSf https://astral.sh/uv/install.sh | sh
    
    # Add to PATH
    export PATH="$HOME/.cargo/bin:$PATH"
    echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.bashrc
    
    # Make UV available system-wide
    if [ -f "$HOME/.cargo/bin/uv" ]; then
        sudo cp "$HOME/.cargo/bin/uv" /usr/local/bin/
        sudo chmod +x /usr/local/bin/uv
        print_success "UV installed successfully"
    else
        print_error "UV installation failed"
        exit 1
    fi
fi

# Step 3: Test UV functionality
print_status "Step 3: Testing UV functionality..."

if uv --version &> /dev/null; then
    print_success "UV is working: $(uv --version)"
else
    print_error "UV is not working properly"
    exit 1
fi

# Step 4: Test MCP server installation
print_status "Step 4: Testing MCP server installation..."

echo "Testing AWS pricing MCP server..."
if timeout 30 uv tool run --from awslabs-aws-pricing-mcp-server@latest awslabs-aws-pricing-mcp-server --help &> /dev/null; then
    print_success "AWS pricing MCP server can be installed"
else
    print_warning "AWS pricing MCP server installation may have issues"
fi

# Step 5: Check Python MCP library
print_status "Step 5: Checking Python MCP library..."

cd /opt/mcp-client
if sudo ./venv/bin/python -c "import mcp; print(f'MCP version: {mcp.__version__}')" 2>/dev/null; then
    print_success "MCP Python library is working"
else
    print_warning "MCP Python library has issues - reinstalling..."
    sudo ./venv/bin/pip install mcp --upgrade
fi

# Step 6: Update service configuration
print_status "Step 6: Updating service configuration..."

# Backup current service file
sudo cp /etc/systemd/system/mcp-client.service /etc/systemd/system/mcp-client.service.backup

# Update service file to include proper PATH
sudo tee /etc/systemd/system/mcp-client.service > /dev/null <<'EOF'
[Unit]
Description=MCP Client FastAPI Backend Service
Documentation=https://fastapi.tiangolo.com/
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/mcp-client
Environment=AWS_REGION=ap-south-1
Environment=BEDROCK_MODEL_ID=apac.anthropic.claude-3-7-sonnet-20250219-v1:0
Environment=PATH=/usr/local/bin:/usr/bin:/bin:/root/.cargo/bin
ExecStart=/opt/mcp-client/venv/bin/gunicorn main:app \
    --bind 0.0.0.0:8000 \
    --workers 2 \
    --worker-class uvicorn.workers.UvicornWorker \
    --timeout 300 \
    --max-requests 1000 \
    --max-requests-jitter 100 \
    --preload \
    --access-logfile /var/log/mcp-client/access.log \
    --error-logfile /var/log/mcp-client/error.log \
    --log-level info
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings (simplified)
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF

print_success "Service configuration updated with proper PATH"

# Step 7: Create simplified MCP config for testing
print_status "Step 7: Creating simplified MCP configuration..."

sudo tee /opt/mcp-client/server_config_test.json > /dev/null <<'EOF'
{
  "mcpServers": {
    "pricing": {
      "command": "uv",
      "args": [
        "tool",
        "run",
        "--from",
        "awslabs-aws-pricing-mcp-server@latest",
        "awslabs-aws-pricing-mcp-server"
      ],
      "env": {
        "AWS_REGION": "ap-south-1"
      }
    }
  }
}
EOF

# Backup original config and use test config
sudo cp /opt/mcp-client/server_config.json /opt/mcp-client/server_config.json.backup
sudo cp /opt/mcp-client/server_config_test.json /opt/mcp-client/server_config.json

print_success "Simplified MCP configuration created"

# Step 8: Restart service
print_status "Step 8: Restarting MCP Client service..."

sudo systemctl daemon-reload
sudo systemctl restart mcp-client

# Wait for service to start
sleep 10

# Check service status
if sudo systemctl is-active --quiet mcp-client; then
    print_success "MCP Client service is running"
else
    print_error "MCP Client service failed to start"
    echo "Service status:"
    sudo systemctl status mcp-client
    echo -e "\nRecent logs:"
    sudo journalctl -u mcp-client -n 20
    exit 1
fi

# Step 9: Test endpoints again
print_status "Step 9: Testing endpoints after fixes..."

sleep 5

echo "Testing servers endpoint:"
SERVERS_RESPONSE=$(curl -s http://localhost:8000/servers)
echo $SERVERS_RESPONSE

echo -e "\nTesting tools endpoint:"
TOOLS_RESPONSE=$(curl -s http://localhost:8000/tools)
echo $TOOLS_RESPONSE

# Step 10: Test chat with tools
print_status "Step 10: Testing chat with tools..."

echo "Testing chat endpoint with tools:"
curl -s -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "What is the pricing for AWS Lambda?", "use_tools": true}' | \
  jq '.tools_used' 2>/dev/null || echo "Tools used info not available"

# Step 11: Check logs for errors
print_status "Step 11: Checking recent logs for errors..."

echo "Recent service logs:"
sudo journalctl -u mcp-client -n 10

echo -e "\nError log:"
sudo tail -n 10 /var/log/mcp-client/error.log 2>/dev/null || echo "No error log found"

# Step 12: Final status
print_status "Step 12: Final status check..."

if [[ "$SERVERS_RESPONSE" != "{}" ]]; then
    print_success "✅ MCP servers are now working!"
    echo "🌐 Your API is available at: http://************:8000"
    echo "📖 API Documentation: http://************:8000/docs"
else
    print_warning "⚠️ MCP servers still not working"
    echo "Manual debugging needed. Check logs:"
    echo "sudo journalctl -u mcp-client -f"
fi

echo -e "\n🎯 Quick test commands:"
echo "curl http://************:8000/servers"
echo "curl http://************:8000/tools"
echo 'curl -X POST http://************:8000/chat -H "Content-Type: application/json" -d '"'"'{"message": "Test tools", "use_tools": true}'"'"

print_success "Script completed!"
