#!/bin/bash

# Nginx Setup Script for MCP Client
# Run this script after the main deployment

set -e

DOMAIN="your-domain.com"  # Replace with your actual domain
EMAIL="<EMAIL>"  # Replace with your email for Let's Encrypt

echo "Setting up Nginx reverse proxy for MCP Client..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Prompt for domain if not set
if [ "$DOMAIN" = "your-domain.com" ]; then
    read -p "Enter your domain name: " DOMAIN
fi

if [ "$EMAIL" = "<EMAIL>" ]; then
    read -p "Enter your email for Let's Encrypt: " EMAIL
fi

# Update the nginx configuration with the actual domain
log_info "Updating Nginx configuration with domain: $DOMAIN"
sed -i "s/your-domain.com/$DOMAIN/g" nginx-mcp-client.conf

# Copy nginx configuration
log_info "Installing Nginx configuration..."
cp nginx-mcp-client.conf /etc/nginx/sites-available/mcp-client

# Enable the site
log_info "Enabling Nginx site..."
ln -sf /etc/nginx/sites-available/mcp-client /etc/nginx/sites-enabled/

# Remove default nginx site
rm -f /etc/nginx/sites-enabled/default

# Test nginx configuration
log_info "Testing Nginx configuration..."
nginx -t

# Restart nginx
log_info "Restarting Nginx..."
systemctl restart nginx

# Obtain SSL certificate with Let's Encrypt
log_info "Obtaining SSL certificate with Let's Encrypt..."
certbot --nginx -d $DOMAIN -d www.$DOMAIN --email $EMAIL --agree-tos --non-interactive

# Setup automatic certificate renewal
log_info "Setting up automatic certificate renewal..."
systemctl enable certbot.timer
systemctl start certbot.timer

# Test certificate renewal
log_info "Testing certificate renewal..."
certbot renew --dry-run

log_info "Nginx setup completed successfully!"
log_info "Your application should now be accessible at: https://$DOMAIN"
log_info "SSL certificate will auto-renew via systemd timer"
