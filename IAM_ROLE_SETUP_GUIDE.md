# IAM Role Setup Guide for MCP Client

This guide shows you how to set up IAM roles for your EC2 instance instead of using AWS credentials in environment files. This is the **recommended and more secure approach**.

## Why Use IAM Roles?

### ✅ **Benefits:**
- **No credentials in files** - More secure
- **Automatic credential rotation** - AWS handles this
- **No credential management** - No keys to manage
- **Easier deployment** - No .env file configuration needed
- **Better security** - Follows AWS best practices

### ❌ **vs Environment Variables:**
- Environment variables require storing sensitive keys
- Risk of credential exposure in logs or code
- Manual credential rotation required

## Step 1: Create IAM Policy

### **1.1 Create Policy via AWS Console:**

1. Go to **IAM Console** → **Policies** → **Create Policy**
2. Choose **JSON** tab and paste this policy:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": [
                "arn:aws:bedrock:*:*:foundation-model/anthropic.claude-*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "pricing:GetProducts",
                "pricing:DescribeServices"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "ce:GetCostAndUsage",
                "ce:GetUsageReport",
                "ce:GetDimensionValues"
            ],
            "Resource": "*"
        }
    ]
}
```

3. **Name**: `MCP-Client-Policy`
4. **Description**: `Policy for MCP Client to access Bedrock and AWS services`
5. Click **Create Policy**

### **1.2 Create Policy via AWS CLI:**

```bash
# Create policy file
cat > mcp-client-policy.json <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": [
                "arn:aws:bedrock:*:*:foundation-model/anthropic.claude-*"
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "pricing:GetProducts",
                "pricing:DescribeServices"
            ],
            "Resource": "*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "ce:GetCostAndUsage",
                "ce:GetUsageReport",
                "ce:GetDimensionValues"
            ],
            "Resource": "*"
        }
    ]
}
EOF

# Create the policy
aws iam create-policy \
    --policy-name MCP-Client-Policy \
    --policy-document file://mcp-client-policy.json \
    --description "Policy for MCP Client to access Bedrock and AWS services"
```

## Step 2: Create IAM Role

### **2.1 Create Role via AWS Console:**

1. Go to **IAM Console** → **Roles** → **Create Role**
2. **Trusted entity type**: AWS service
3. **Service**: EC2
4. **Use case**: EC2
5. Click **Next**
6. **Attach permissions**: Search and select `MCP-Client-Policy`
7. Click **Next**
8. **Role name**: `MCP-Client-EC2-Role`
9. **Description**: `IAM role for EC2 instances running MCP Client`
10. Click **Create Role**

### **2.2 Create Role via AWS CLI:**

```bash
# Create trust policy for EC2
cat > trust-policy.json <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "ec2.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF

# Create the role
aws iam create-role \
    --role-name MCP-Client-EC2-Role \
    --assume-role-policy-document file://trust-policy.json \
    --description "IAM role for EC2 instances running MCP Client"

# Attach the policy to the role
aws iam attach-role-policy \
    --role-name MCP-Client-EC2-Role \
    --policy-arn arn:aws:iam::YOUR_ACCOUNT_ID:policy/MCP-Client-Policy

# Create instance profile
aws iam create-instance-profile \
    --instance-profile-name MCP-Client-EC2-Profile

# Add role to instance profile
aws iam add-role-to-instance-profile \
    --instance-profile-name MCP-Client-EC2-Profile \
    --role-name MCP-Client-EC2-Role
```

## Step 3: Launch EC2 Instance with IAM Role

### **3.1 Via AWS Console:**

1. **Launch Instance** as usual
2. In **Advanced Details** section:
3. **IAM instance profile**: Select `MCP-Client-EC2-Role`
4. Complete the launch process

### **3.2 Via AWS CLI:**

```bash
aws ec2 run-instances \
    --image-id ami-0c02fb55956c7d316 \
    --instance-type t3.small \
    --key-name your-key-pair \
    --security-group-ids sg-xxxxxxxxx \
    --subnet-id subnet-xxxxxxxxx \
    --iam-instance-profile Name=MCP-Client-EC2-Profile \
    --associate-public-ip-address \
    --block-device-mappings '[{"DeviceName":"/dev/sda1","Ebs":{"VolumeSize":20,"VolumeType":"gp3"}}]' \
    --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=MCP-Client-IAM}]'
```

## Step 4: Attach IAM Role to Existing Instance

If you already have an EC2 instance running:

### **4.1 Via AWS Console:**

1. Go to **EC2 Console** → **Instances**
2. Select your instance
3. **Actions** → **Security** → **Modify IAM role**
4. **IAM role**: Select `MCP-Client-EC2-Role`
5. Click **Update IAM role**

### **4.2 Via AWS CLI:**

```bash
# Associate IAM instance profile with existing instance
aws ec2 associate-iam-instance-profile \
    --instance-id i-1234567890abcdef0 \
    --iam-instance-profile Name=MCP-Client-EC2-Profile
```

## Step 5: Deploy Application

Now deploy your application using the updated scripts:

```bash
# 1. Connect to your instance
ssh -i your-key.pem ec2-user@your-instance-ip

# 2. Upload/create your files
nano main.py          # Copy your updated main.py
nano server_config.json
nano requirements.txt
nano amazon-linux-deploy.sh  # Copy the updated deployment script

# 3. Run deployment
chmod +x amazon-linux-deploy.sh
sudo ./amazon-linux-deploy.sh

# 4. Start the service (no .env configuration needed!)
sudo systemctl start mcp-client
sudo systemctl status mcp-client
```

## Step 6: Verify IAM Role is Working

### **6.1 Test AWS Access:**

```bash
# On your EC2 instance, test AWS access
aws sts get-caller-identity

# Should return something like:
# {
#     "UserId": "AROAXXXXXXXXXXXXX:i-1234567890abcdef0",
#     "Account": "************",
#     "Arn": "arn:aws:sts::************:assumed-role/MCP-Client-EC2-Role/i-1234567890abcdef0"
# }
```

### **6.2 Test Bedrock Access:**

```bash
# Test Bedrock access
aws bedrock list-foundation-models --region ap-south-1

# Should list available models without authentication errors
```

### **6.3 Check Application Logs:**

```bash
# Check if application starts without credential errors
sudo journalctl -u mcp-client -f

# Should see: "Bedrock client initialized successfully using IAM role"
```

## Troubleshooting

### **Common Issues:**

1. **"Unable to locate credentials" error:**
   ```bash
   # Check if IAM role is attached
   curl http://169.254.169.254/latest/meta-data/iam/security-credentials/
   
   # Should return the role name: MCP-Client-EC2-Role
   ```

2. **"Access Denied" errors:**
   ```bash
   # Verify policy permissions
   aws iam get-role-policy --role-name MCP-Client-EC2-Role --policy-name MCP-Client-Policy
   ```

3. **Instance profile not found:**
   ```bash
   # Check if instance profile exists
   aws iam get-instance-profile --instance-profile-name MCP-Client-EC2-Profile
   ```

## Security Benefits

### ✅ **What You Get:**
- **No credentials in code or files**
- **Automatic credential rotation**
- **Principle of least privilege**
- **Audit trail via CloudTrail**
- **No credential management overhead**

### 🔒 **Best Practices:**
- Use specific resource ARNs when possible
- Regularly review and audit permissions
- Use separate roles for different environments
- Monitor usage via CloudTrail

Your MCP Client will now securely access AWS services using IAM roles without any credentials stored on the instance!
