[Unit]
Description=MCP Client FastAPI Application
After=network.target
Wants=network.target

[Service]
Type=exec
User=root
Group=root
WorkingDirectory=/opt/mcp-client
Environment=PATH=/opt/mcp-client/venv/bin
Environment=PYTHONPATH=/opt/mcp-client
EnvironmentFile=/opt/mcp-client/.env
ExecStart=/opt/mcp-client/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4 --access-log --log-level info
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/mcp-client /var/log/mcp-client /tmp
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
