# Python 3.10+ Installation Guide for Amazon Linux

This guide helps you install Python 3.10 or higher on Amazon Linux for MCP Client deployment.

## Quick Python Version Check

```bash
# Check current Python version
python3 --version

# Check Amazon Linux version
cat /etc/os-release
```

## Option 1: Amazon Linux 2023 (Recommended)

Amazon Linux 2023 has Python 3.11 available in the default repositories:

```bash
# Install Python 3.11
sudo yum install -y python3.11 python3.11-pip python3.11-devel

# Set as default python3
sudo alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
sudo alternatives --install /usr/bin/pip3 pip3 /usr/bin/pip3.11 1

# Verify installation
python3 --version  # Should show Python 3.11.x
```

## Option 2: Amazon Linux 2 (Manual Installation)

Amazon Linux 2 comes with Python 3.7, so we need to install Python 3.10 manually:

### Step 1: Install Build Dependencies

```bash
# Install development tools
sudo yum groupinstall -y "Development Tools"

# Install required libraries
sudo yum install -y openssl-devel bzip2-devel libffi-devel zlib-devel \
    readline-devel sqlite-devel tk-devel gdbm-devel db4-devel \
    libpcap-devel xz-devel expat-devel
```

### Step 2: Download and Compile Python 3.10

```bash
# Create temporary directory
cd /tmp

# Download Python 3.10.12 (stable version)
wget https://www.python.org/ftp/python/3.10.12/Python-3.10.12.tgz

# Extract the archive
tar xzf Python-3.10.12.tgz
cd Python-3.10.12

# Configure build (this may take 2-3 minutes)
./configure --enable-optimizations --with-ensurepip=install --prefix=/usr/local

# Compile Python (this may take 10-15 minutes)
make -j $(nproc)

# Install Python
sudo make altinstall
```

### Step 3: Create Symlinks

```bash
# Create symlinks for easier access
sudo ln -sf /usr/local/bin/python3.10 /usr/bin/python3
sudo ln -sf /usr/local/bin/pip3.10 /usr/bin/pip3

# Verify installation
python3 --version  # Should show Python 3.10.12
pip3 --version     # Should show pip for Python 3.10
```

### Step 4: Clean Up

```bash
# Remove temporary files
cd /
rm -rf /tmp/Python-3.10.12*

# Update pip
pip3 install --upgrade pip
```

## Option 3: Using pyenv (Alternative Method)

If you prefer using pyenv for Python version management:

```bash
# Install pyenv dependencies
sudo yum install -y git gcc openssl-devel bzip2-devel libffi-devel \
    zlib-devel readline-devel sqlite-devel

# Install pyenv
curl https://pyenv.run | bash

# Add to shell profile
echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc
echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc
echo 'eval "$(pyenv init -)"' >> ~/.bashrc

# Reload shell
source ~/.bashrc

# Install Python 3.10
pyenv install 3.10.12
pyenv global 3.10.12

# Verify
python --version  # Should show Python 3.10.12
```

## Verification Commands

After installing Python 3.10+, verify everything works:

```bash
# Check Python version
python3 --version

# Check pip version
pip3 --version

# Test virtual environment creation
python3 -m venv test_venv
source test_venv/bin/activate
python --version
deactivate
rm -rf test_venv

# Test MCP installation
pip3 install mcp
python3 -c "import mcp; print(f'MCP version: {mcp.__version__}')"
```

## Troubleshooting

### Build Errors During Compilation

```bash
# If you get SSL errors
sudo yum install -y openssl11-devel
export LDFLAGS="-L/usr/lib64/openssl11"
export CPPFLAGS="-I/usr/include/openssl11"

# If you get zlib errors
sudo yum install -y zlib-devel

# If you get readline errors
sudo yum install -y readline-devel
```

### Permission Issues

```bash
# If you get permission errors with pip
pip3 install --user package_name

# Or use sudo for system-wide installation
sudo pip3 install package_name
```

### Multiple Python Versions

```bash
# List all Python installations
ls -la /usr/bin/python*
ls -la /usr/local/bin/python*

# Use specific version
/usr/local/bin/python3.10 --version
```

## Performance Notes

- **Compilation time**: 10-15 minutes on t3.small, 5-8 minutes on t3.medium
- **Disk space**: Python 3.10 installation requires ~150MB
- **Memory**: Compilation needs at least 1GB RAM (use swap if needed)

## Quick Setup Script

Here's a complete script for Amazon Linux 2:

```bash
#!/bin/bash
# Quick Python 3.10 installation script for Amazon Linux 2

echo "Installing Python 3.10 on Amazon Linux 2..."

# Install dependencies
sudo yum groupinstall -y "Development Tools"
sudo yum install -y openssl-devel bzip2-devel libffi-devel zlib-devel

# Download and install Python 3.10
cd /tmp
wget https://www.python.org/ftp/python/3.10.12/Python-3.10.12.tgz
tar xzf Python-3.10.12.tgz
cd Python-3.10.12
./configure --enable-optimizations --with-ensurepip=install
make -j $(nproc)
sudo make altinstall

# Create symlinks
sudo ln -sf /usr/local/bin/python3.10 /usr/bin/python3
sudo ln -sf /usr/local/bin/pip3.10 /usr/bin/pip3

# Clean up
cd /
rm -rf /tmp/Python-3.10.12*

# Verify
echo "Python installation complete!"
python3 --version
pip3 --version
```

Save this as `install_python310.sh`, make it executable with `chmod +x install_python310.sh`, and run with `./install_python310.sh`.

## Next Steps

After installing Python 3.10+, you can proceed with the MCP Client deployment using the main deployment guide.

The key requirements for MCP are:
- ✅ Python 3.10 or higher
- ✅ pip 21.0 or higher
- ✅ Virtual environment support

Your system is now ready for MCP Client deployment! 🚀
