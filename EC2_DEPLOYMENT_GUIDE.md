# MCP Client FastAPI Deployment Guide for AWS EC2

This guide provides step-by-step instructions for deploying your MCP Client FastAPI application on AWS EC2.

## Prerequisites

- AWS Account with appropriate permissions
- Domain name (optional but recommended for production)
- AWS CLI configured locally (for file transfer)
- SSH key pair for EC2 access

## Step 1: Launch EC2 Instance

### Recommended Instance Configuration

**Instance Type**: `t3.medium` or `t3.large` (minimum t3.small)
- **vCPUs**: 2-4
- **Memory**: 4-8 GB RAM
- **Storage**: 20-30 GB GP3 SSD

**Operating System**: Ubuntu 22.04 LTS (ami-0c02fb55956c7d316)

### Security Group Configuration

Create a security group with the following rules:

| Type | Protocol | Port Range | Source | Description |
|------|----------|------------|--------|-------------|
| SSH | TCP | 22 | Your IP/0.0.0.0/0 | SSH access |
| HTTP | TCP | 80 | 0.0.0.0/0 | HTTP traffic |
| HTTPS | TCP | 443 | 0.0.0.0/0 | HTTPS traffic |
| Custom TCP | TCP | 8000 | Your IP | Direct app access (temporary) |

### Launch Steps

1. **Launch Instance**:
   ```bash
   aws ec2 run-instances \
     --image-id ami-0c02fb55956c7d316 \
     --instance-type t3.medium \
     --key-name your-key-pair \
     --security-group-ids sg-xxxxxxxxx \
     --subnet-id subnet-xxxxxxxxx \
     --associate-public-ip-address \
     --block-device-mappings '[{"DeviceName":"/dev/sda1","Ebs":{"VolumeSize":20,"VolumeType":"gp3"}}]' \
     --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=MCP-Client-Server}]'
   ```

2. **Get Instance Public IP**:
   ```bash
   aws ec2 describe-instances --instance-ids i-xxxxxxxxx --query 'Reservations[0].Instances[0].PublicIpAddress'
   ```

## Step 2: Connect to EC2 Instance

```bash
ssh -i your-key.pem ubuntu@your-instance-public-ip
```

## Step 3: Initial Server Setup

Run the EC2 setup script:

```bash
# Download and run the setup script
wget https://raw.githubusercontent.com/your-repo/setup-ec2.sh
chmod +x setup-ec2.sh
sudo ./setup-ec2.sh
```

Or manually execute the setup steps from `setup-ec2.sh`.

## Step 4: Upload Application Files

From your local machine, upload the application files:

```bash
# Create a deployment package
tar -czf mcp-client-deploy.tar.gz \
  main.py \
  server_config.json \
  requirements.txt \
  .env.production \
  mcp-client.service \
  deploy.sh \
  nginx-mcp-client.conf \
  setup-nginx.sh

# Upload to EC2
scp -i your-key.pem mcp-client-deploy.tar.gz ubuntu@your-instance-ip:~/

# Extract on server
ssh -i your-key.pem ubuntu@your-instance-ip
tar -xzf mcp-client-deploy.tar.gz
```

## Step 5: Deploy Application

Run the deployment script:

```bash
sudo ./deploy.sh
```

## Step 6: Configure Environment

Edit the environment file with your actual AWS credentials:

```bash
sudo nano /opt/mcp-client/.env
```

Update the following variables:
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `AWS_REGION`
- `BEDROCK_MODEL_ID`
- `SECRET_KEY` (generate with: `openssl rand -hex 32`)

## Step 7: Start the Service

```bash
# Start the MCP Client service
sudo systemctl start mcp-client

# Check status
sudo systemctl status mcp-client

# View logs
sudo journalctl -u mcp-client -f
```

## Step 8: Setup Nginx Reverse Proxy (Optional but Recommended)

If you have a domain name:

```bash
# Edit the setup script with your domain
nano setup-nginx.sh

# Run the nginx setup
sudo ./setup-nginx.sh
```

## Step 9: Verify Deployment

### Test Direct Access
```bash
curl http://localhost:8000/
```

### Test via Nginx (if configured)
```bash
curl https://your-domain.com/
```

### Test API Endpoints
```bash
# Health check
curl https://your-domain.com/

# List servers
curl https://your-domain.com/servers

# List tools
curl https://your-domain.com/tools
```

## Step 10: Monitoring and Maintenance

### View Application Logs
```bash
# Application logs
sudo journalctl -u mcp-client -f

# Nginx logs
sudo tail -f /var/log/nginx/mcp-client-access.log
sudo tail -f /var/log/nginx/mcp-client-error.log

# Application-specific logs
sudo tail -f /var/log/mcp-client/app.log
```

### Service Management
```bash
# Restart service
sudo systemctl restart mcp-client

# Stop service
sudo systemctl stop mcp-client

# Check service status
sudo systemctl status mcp-client

# Enable auto-start on boot
sudo systemctl enable mcp-client
```

### Update Application
```bash
# Stop service
sudo systemctl stop mcp-client

# Update files
sudo cp new-main.py /opt/mcp-client/main.py
sudo chown mcpuser:mcpuser /opt/mcp-client/main.py

# Restart service
sudo systemctl start mcp-client
```

## Troubleshooting

### Common Issues

1. **Service won't start**:
   ```bash
   sudo journalctl -u mcp-client --no-pager
   ```

2. **Permission errors**:
   ```bash
   sudo chown -R mcpuser:mcpuser /opt/mcp-client
   ```

3. **Port already in use**:
   ```bash
   sudo netstat -tlnp | grep :8000
   sudo kill -9 <PID>
   ```

4. **AWS credentials issues**:
   - Verify credentials in `/opt/mcp-client/.env`
   - Test with: `aws sts get-caller-identity`

### Performance Tuning

1. **Increase worker processes** (edit `/etc/systemd/system/mcp-client.service`):
   ```
   ExecStart=/opt/mcp-client/venv/bin/gunicorn main:app -w 8 -k uvicorn.workers.UvicornWorker --bind 127.0.0.1:8000
   ```

2. **Monitor resource usage**:
   ```bash
   htop
   df -h
   free -h
   ```

## Cost Optimization

### Instance Right-Sizing
- Start with `t3.small` for testing
- Monitor CPU/memory usage and scale up if needed
- Consider Reserved Instances for long-term deployments

### Storage Optimization
- Use GP3 volumes for better price/performance
- Enable EBS optimization
- Set up automated snapshots

## Backup Strategy

### Application Backup
```bash
# Create backup script
sudo tee /opt/backup-mcp-client.sh > /dev/null <<EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf /opt/backups/mcp-client-\$DATE.tar.gz /opt/mcp-client
aws s3 cp /opt/backups/mcp-client-\$DATE.tar.gz s3://your-backup-bucket/
find /opt/backups -name "mcp-client-*.tar.gz" -mtime +7 -delete
EOF

chmod +x /opt/backup-mcp-client.sh
```

### Automated Backups
```bash
# Add to crontab
echo "0 2 * * * /opt/backup-mcp-client.sh" | sudo crontab -
```

## Next Steps

- Set up monitoring with CloudWatch
- Configure automated backups
- Implement CI/CD pipeline
- Set up log aggregation
- Configure alerts and notifications
