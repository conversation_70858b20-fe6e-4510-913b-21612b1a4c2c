#!/bin/bash

# Comprehensive MCP Server Diagnostic and Fix Script
# This script diagnoses and fixes all MCP server integration issues

set -e

echo "🔍 Comprehensive MCP Server Diagnostic and Fix Script"
echo "====================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

print_fix() {
    echo -e "${CYAN}[FIX]${NC} $1"
}

# Global variables
MCP_INTEGRATION_NEEDED=false
MCP_SERVERS_WORKING=false
UV_ACCESSIBLE=false

# Step 1: Examine MCP server integration in main.py
print_status "Step 1: Examining MCP server integration in main.py..."

cd /opt/mcp-client

# Check if main.py exists
if [[ ! -f "main.py" ]]; then
    print_error "main.py not found!"
    exit 1
fi

# Analyze MCP-related code in main.py
print_debug "Checking for MCP imports..."
MCP_IMPORTS=$(grep -n "import.*mcp\|from.*mcp" main.py || echo "")
if [[ -n "$MCP_IMPORTS" ]]; then
    print_success "Found MCP imports:"
    echo "$MCP_IMPORTS"
else
    print_warning "No MCP imports found in main.py"
fi

print_debug "Checking for MCP server configuration loading..."
CONFIG_LOADING=$(grep -n "server_config\|mcpServers" main.py || echo "")
if [[ -n "$CONFIG_LOADING" ]]; then
    print_success "Found server config references:"
    echo "$CONFIG_LOADING"
else
    print_warning "No server config loading found in main.py"
fi

print_debug "Checking /servers endpoint implementation..."
SERVERS_ENDPOINT=$(grep -A 20 "@app.get(\"/servers\")" main.py || echo "")
if [[ -n "$SERVERS_ENDPOINT" ]]; then
    print_success "Found /servers endpoint:"
    echo "$SERVERS_ENDPOINT"
else
    print_warning "No /servers endpoint found in main.py"
fi

print_debug "Checking /tools endpoint implementation..."
TOOLS_ENDPOINT=$(grep -A 20 "@app.get(\"/tools\")" main.py || echo "")
if [[ -n "$TOOLS_ENDPOINT" ]]; then
    print_success "Found /tools endpoint:"
    echo "$TOOLS_ENDPOINT"
else
    print_warning "No /tools endpoint found in main.py"
fi

# Step 2: Check current API responses
print_status "Step 2: Testing current API responses..."

echo "Health check:"
curl -s http://localhost:8000/ | jq '.' 2>/dev/null || curl -s http://localhost:8000/

echo -e "\nServers endpoint:"
SERVERS_RESPONSE=$(curl -s http://localhost:8000/servers)
echo $SERVERS_RESPONSE

echo -e "\nTools endpoint:"
TOOLS_RESPONSE=$(curl -s http://localhost:8000/tools)
echo $TOOLS_RESPONSE

# Analyze responses
if [[ "$SERVERS_RESPONSE" == "{}" ]]; then
    print_warning "Servers endpoint returns empty object - MCP integration missing or broken"
    MCP_INTEGRATION_NEEDED=true
else
    print_success "Servers endpoint returns data"
    MCP_INTEGRATION_NEEDED=false
fi

if [[ "$TOOLS_RESPONSE" == "{}" ]]; then
    print_warning "Tools endpoint returns empty object - MCP integration missing or broken"
else
    print_success "Tools endpoint returns data"
fi

# Step 3: Verify MCP server configuration loading
print_status "Step 3: Verifying MCP server configuration loading..."

if [[ -f "server_config.json" ]]; then
    print_success "server_config.json exists"
    
    # Validate JSON syntax
    if python3 -m json.tool server_config.json > /dev/null 2>&1; then
        print_success "server_config.json is valid JSON"
        
        # Show config content
        print_debug "Configuration content:"
        cat server_config.json | jq '.' 2>/dev/null || cat server_config.json
        
        # Check if config has servers
        SERVER_COUNT=$(python3 -c "import json; config=json.load(open('server_config.json')); print(len(config.get('mcpServers', {})))")
        if [[ "$SERVER_COUNT" -gt 0 ]]; then
            print_success "Found $SERVER_COUNT MCP server(s) in configuration"
        else
            print_warning "No MCP servers found in configuration"
        fi
    else
        print_error "server_config.json is invalid JSON"
        exit 1
    fi
else
    print_error "server_config.json not found"
    exit 1
fi

# Step 4: Check UV installation and accessibility
print_status "Step 4: Checking UV tool installation and accessibility..."

# Check if UV is in PATH
if command -v uv &> /dev/null; then
    print_success "UV is accessible: $(uv --version)"
    UV_ACCESSIBLE=true
else
    print_warning "UV not found in current PATH"
    UV_ACCESSIBLE=false
    
    # Look for UV in common locations
    UV_LOCATIONS=(
        "/root/.cargo/bin/uv"
        "/usr/local/bin/uv"
        "/usr/bin/uv"
        "$HOME/.cargo/bin/uv"
    )
    
    for location in "${UV_LOCATIONS[@]}"; do
        if [[ -f "$location" ]]; then
            print_success "Found UV at: $location"
            export PATH="$(dirname $location):$PATH"
            UV_ACCESSIBLE=true
            break
        fi
    done
    
    if [[ "$UV_ACCESSIBLE" == "false" ]]; then
        print_warning "UV not found - installing..."
        
        # Install UV
        print_status "Installing UV tool..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        
        # Add to PATH
        export PATH="$HOME/.cargo/bin:$PATH"
        echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.bashrc
        
        # Make UV available system-wide
        if [ -f "$HOME/.cargo/bin/uv" ]; then
            sudo cp "$HOME/.cargo/bin/uv" /usr/local/bin/
            sudo chmod +x /usr/local/bin/uv
            print_success "UV installed successfully"
            UV_ACCESSIBLE=true
        else
            print_error "UV installation failed"
            exit 1
        fi
    fi
fi

# Step 5: Test MCP client functionality
print_status "Step 5: Testing MCP client functionality..."

# Test UV functionality
if uv --version &> /dev/null; then
    print_success "UV is working: $(uv --version)"
else
    print_error "UV is not working properly"
    exit 1
fi

# Test MCP server installation
print_debug "Testing AWS pricing MCP server installation..."
if timeout 30 uv tool run --from awslabs-aws-pricing-mcp-server@latest awslabs-aws-pricing-mcp-server --help &> /dev/null; then
    print_success "AWS pricing MCP server can be installed and run"
else
    print_warning "AWS pricing MCP server installation may have issues"
fi

# Test Python MCP library
print_debug "Testing Python MCP library..."
if ./venv/bin/python -c "import mcp; from mcp import ClientSession, StdioServerParameters; from mcp.client.stdio import stdio_client; print('MCP library working')" 2>/dev/null; then
    print_success "Python MCP library is functional"
else
    print_warning "Python MCP library has issues - reinstalling..."
    ./venv/bin/pip install mcp --upgrade
fi

# Create MCP test script
print_debug "Creating MCP connection test..."
cat > test_mcp_connection.py << 'EOF'
#!/usr/bin/env python3
import asyncio
import json
import os
import sys
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_mcp_server(server_name, server_config):
    try:
        print(f"Testing {server_name}...")
        
        server_params = StdioServerParameters(
            command=server_config['command'],
            args=server_config['args'],
            env=server_config.get('env', {})
        )
        
        # Test with timeout
        async with asyncio.timeout(30):
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()
                    print(f"✅ Successfully connected to {server_name}")
                    
                    # List tools
                    tools = await session.list_tools()
                    print(f"Tools available: {[tool.name for tool in tools.tools]}")
                    return True
                    
    except Exception as e:
        print(f"❌ Failed to connect to {server_name}: {e}")
        return False

async def main():
    try:
        with open('server_config.json', 'r') as f:
            config = json.load(f)
        
        servers = config.get('mcpServers', {})
        if not servers:
            print("No MCP servers configured")
            return False
            
        success_count = 0
        for server_name, server_config in servers.items():
            if await test_mcp_server(server_name, server_config):
                success_count += 1
                
        print(f"Successfully connected to {success_count}/{len(servers)} servers")
        return success_count > 0
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
EOF

# Run MCP connection test
print_debug "Running MCP connection test..."
if ./venv/bin/python test_mcp_connection.py; then
    print_success "MCP servers can be connected to successfully"
    MCP_SERVERS_WORKING=true
else
    print_warning "MCP servers cannot be connected to"
    MCP_SERVERS_WORKING=false
fi

# Step 6: Implement missing MCP integration
print_status "Step 6: Implementing missing MCP integration..."

if [[ "$MCP_INTEGRATION_NEEDED" == "true" ]] || [[ "$MCP_SERVERS_WORKING" == "false" ]]; then
    print_fix "Adding comprehensive MCP integration to main.py..."

    # Backup original main.py
    cp main.py main.py.backup

    # Create enhanced main.py with full MCP integration
    cat > main_with_mcp.py << 'EOF'
import asyncio
import json
import logging
import os
import uuid
from contextlib import asynccontextmanager
from typing import Dict, List, Optional, Any

import boto3
from botocore.config import Config
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel

# MCP imports
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChatRequest(BaseModel):
    message: str
    use_tools: bool = False
    conversation_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    tools_used: List[str] = []
    status: str = "success"

class MCPServerManager:
    def __init__(self):
        self.servers: Dict[str, Any] = {}
        self.server_configs = {}
        self.tools_cache = {}

    async def load_server_config(self):
        """Load MCP server configuration from file"""
        try:
            config_path = os.getenv('MCP_CONFIG_PATH', '/opt/mcp-client/server_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    self.server_configs = config.get('mcpServers', {})
                    logger.info(f"Loaded {len(self.server_configs)} MCP server configurations")
            else:
                logger.warning(f"MCP config file not found: {config_path}")
        except Exception as e:
            logger.error(f"Error loading MCP server config: {e}")

    async def connect_to_servers(self):
        """Connect to all configured MCP servers"""
        for server_name, config in self.server_configs.items():
            try:
                await self._connect_server(server_name, config)
            except Exception as e:
                logger.error(f"Failed to connect to {server_name}: {e}")

    async def _connect_server(self, server_name: str, config: dict):
        """Connect to a single MCP server"""
        try:
            server_params = StdioServerParameters(
                command=config['command'],
                args=config['args'],
                env=config.get('env', {})
            )

            # Store server info
            self.servers[server_name] = {
                'config': config,
                'params': server_params,
                'status': 'connecting'
            }

            # Test connection
            async with stdio_client(server_params) as (read, write):
                async with ClientSession(read, write) as session:
                    await session.initialize()

                    # Get available tools
                    tools_result = await session.list_tools()
                    tools = [tool.name for tool in tools_result.tools]

                    self.servers[server_name]['status'] = 'connected'
                    self.servers[server_name]['tools'] = tools
                    self.tools_cache[server_name] = tools_result.tools

                    logger.info(f"Connected to {server_name} with {len(tools)} tools")

        except Exception as e:
            self.servers[server_name]['status'] = 'error'
            self.servers[server_name]['error'] = str(e)
            logger.error(f"Error connecting to {server_name}: {e}")

    async def get_servers_status(self):
        """Get status of all MCP servers"""
        return {
            "servers": [
                {
                    "name": name,
                    "status": info.get('status', 'unknown'),
                    "tools_count": len(info.get('tools', [])),
                    "error": info.get('error')
                }
                for name, info in self.servers.items()
            ]
        }

    async def get_all_tools(self):
        """Get all available tools from all servers"""
        all_tools = []
        for server_name, tools in self.tools_cache.items():
            for tool in tools:
                all_tools.append({
                    "name": tool.name,
                    "description": tool.description,
                    "server": server_name,
                    "schema": tool.inputSchema
                })
        return {"tools": all_tools}

    async def execute_tool(self, tool_name: str, arguments: dict):
        """Execute a tool on the appropriate server"""
        for server_name, tools in self.tools_cache.items():
            for tool in tools:
                if tool.name == tool_name:
                    try:
                        server_params = self.servers[server_name]['params']
                        async with stdio_client(server_params) as (read, write):
                            async with ClientSession(read, write) as session:
                                await session.initialize()
                                result = await session.call_tool(tool_name, arguments)
                                return result
                    except Exception as e:
                        logger.error(f"Error executing tool {tool_name}: {e}")
                        raise
        raise ValueError(f"Tool {tool_name} not found")

class MCPClient:
    def __init__(self):
        self.bedrock_client = None
        self.mcp_manager = MCPServerManager()
        self._initialize_bedrock()

    def _initialize_bedrock(self):
        """Initialize AWS Bedrock client"""
        try:
            config = Config(
                region_name=os.getenv('AWS_REGION', 'ap-south-1'),
                retries={'max_attempts': 3, 'mode': 'adaptive'}
            )

            # Use IAM role credentials (no explicit credentials needed)
            self.bedrock_client = boto3.client('bedrock-runtime', config=config)
            logger.info("Bedrock client initialized successfully using IAM role")
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock client: {e}")
            raise

    async def initialize_mcp(self):
        """Initialize MCP server connections"""
        await self.mcp_manager.load_server_config()
        await self.mcp_manager.connect_to_servers()

    async def chat(self, message: str, use_tools: bool = False, conversation_id: Optional[str] = None) -> ChatResponse:
        """Process chat message with optional tool usage"""
        if not conversation_id:
            conversation_id = f"conv_{uuid.uuid4().hex[:8]}"

        tools_used = []

        try:
            # Prepare the conversation
            messages = [{"role": "user", "content": message}]

            # If tools are requested, get available tools
            available_tools = []
            if use_tools:
                tools_data = await self.mcp_manager.get_all_tools()
                available_tools = tools_data.get('tools', [])

            # Call Bedrock
            model_id = os.getenv('BEDROCK_MODEL_ID', 'apac.anthropic.claude-3-7-sonnet-20250219-v1:0')

            request_body = {
                "anthropic_version": "bedrock-2023-05-31",
                "max_tokens": 4000,
                "messages": messages
            }

            # Add tool definitions if available
            if available_tools and use_tools:
                request_body["tools"] = [
                    {
                        "name": tool["name"],
                        "description": tool["description"],
                        "input_schema": tool["schema"]
                    }
                    for tool in available_tools[:5]  # Limit to 5 tools
                ]

            response = self.bedrock_client.invoke_model(
                modelId=model_id,
                body=json.dumps(request_body)
            )

            response_body = json.loads(response['body'].read())

            # Process response
            if 'content' in response_body:
                content = response_body['content']
                if content and len(content) > 0:
                    response_text = content[0].get('text', '')

                    # Check for tool usage
                    if 'tool_use' in content[0].get('type', ''):
                        # Handle tool usage (simplified)
                        tools_used.append(content[0].get('name', 'unknown_tool'))
                else:
                    response_text = "I apologize, but I couldn't generate a response."
            else:
                response_text = "I apologize, but I couldn't generate a response."

            return ChatResponse(
                response=response_text,
                conversation_id=conversation_id,
                tools_used=tools_used,
                status="success"
            )

        except Exception as e:
            logger.error(f"Error in chat: {e}")
            return ChatResponse(
                response=f"Error: {str(e)}",
                conversation_id=conversation_id,
                tools_used=tools_used,
                status="error"
            )

# Global MCP client instance
mcp_client = MCPClient()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting MCP Client API")
    await mcp_client.initialize_mcp()
    yield
    # Shutdown
    logger.info("Shutting down MCP Client API")

# Create FastAPI app
app = FastAPI(
    title="MCP Client API",
    description="FastAPI backend with MCP server integration and AWS Bedrock",
    version="1.0.0",
    lifespan=lifespan
)

@app.get("/")
async def health_check():
    """Health check endpoint"""
    return {"message": "MCP Client API is running", "status": "healthy"}

@app.get("/servers")
async def get_servers():
    """Get MCP servers status"""
    return await mcp_client.mcp_manager.get_servers_status()

@app.get("/tools")
async def get_tools():
    """Get all available tools from MCP servers"""
    return await mcp_client.mcp_manager.get_all_tools()

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Chat endpoint with optional tool usage"""
    return await mcp_client.chat(
        message=request.message,
        use_tools=request.use_tools,
        conversation_id=request.conversation_id
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
EOF

    # Replace main.py with enhanced version
    mv main_with_mcp.py main.py
    print_success "Enhanced main.py with comprehensive MCP integration created"

else
    print_success "MCP integration appears to be working, skipping implementation"
fi

# Step 7: Update service configuration for MCP
print_status "Step 7: Updating service configuration for MCP support..."

# Update service file with proper environment
sudo tee /etc/systemd/system/mcp-client.service > /dev/null <<EOF
[Unit]
Description=MCP Client FastAPI Backend Service
Documentation=https://fastapi.tiangolo.com/
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/mcp-client
Environment=AWS_REGION=ap-south-1
Environment=BEDROCK_MODEL_ID=apac.anthropic.claude-3-7-sonnet-20250219-v1:0
Environment=MCP_CONFIG_PATH=/opt/mcp-client/server_config.json
Environment=PATH=/root/.cargo/bin:/usr/local/bin:/usr/bin:/bin
Environment=HOME=/root
ExecStart=/opt/mcp-client/venv/bin/gunicorn main:app \\
    --bind 0.0.0.0:8000 \\
    --workers 2 \\
    --worker-class uvicorn.workers.UvicornWorker \\
    --timeout 300 \\
    --max-requests 1000 \\
    --max-requests-jitter 100 \\
    --preload \\
    --access-logfile /var/log/mcp-client/access.log \\
    --error-logfile /var/log/mcp-client/error.log \\
    --log-level info
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=60
TimeoutStopSec=30

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings (simplified)
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF

print_success "Service configuration updated with MCP support"

# Step 8: Restart service and test
print_status "Step 8: Restarting service and testing end-to-end functionality..."

# Reload and restart service
sudo systemctl daemon-reload
sudo systemctl restart mcp-client

# Wait for service to start
sleep 15

# Check service status
if sudo systemctl is-active --quiet mcp-client; then
    print_success "MCP Client service is running"
else
    print_error "MCP Client service failed to start"
    echo "Service status:"
    sudo systemctl status mcp-client
    echo -e "\nRecent logs:"
    sudo journalctl -u mcp-client -n 20
    exit 1
fi

# Step 9: Test end-to-end MCP functionality
print_status "Step 9: Testing end-to-end MCP functionality..."

echo "Testing servers endpoint:"
SERVERS_RESPONSE=$(curl -s http://localhost:8000/servers)
echo $SERVERS_RESPONSE

echo -e "\nTesting tools endpoint:"
TOOLS_RESPONSE=$(curl -s http://localhost:8000/tools)
echo $TOOLS_RESPONSE

echo -e "\nTesting chat with tools:"
CHAT_RESPONSE=$(curl -s -X POST http://localhost:8000/chat \\
  -H "Content-Type: application/json" \\
  -d '{"message": "What AWS services are available for pricing analysis?", "use_tools": true}')
echo $CHAT_RESPONSE | jq '.tools_used' 2>/dev/null || echo "Tools used info not available"

# Final assessment
print_status "Step 10: Final assessment..."

if [[ "$SERVERS_RESPONSE" != "{}" ]]; then
    print_success "✅ MCP servers are now working!"

    # Count servers
    SERVER_COUNT=$(echo $SERVERS_RESPONSE | jq '.servers | length' 2>/dev/null || echo "0")
    print_success "Found $SERVER_COUNT active MCP server(s)"

    if [[ "$TOOLS_RESPONSE" != "{}" ]]; then
        TOOL_COUNT=$(echo $TOOLS_RESPONSE | jq '.tools | length' 2>/dev/null || echo "0")
        print_success "Found $TOOL_COUNT available tool(s)"
    fi

    echo -e "\n🎉 MCP integration is fully functional!"
    echo "🌐 Your API is available at: http://************:8000"
    echo "📖 API Documentation: http://************:8000/docs"

else
    print_warning "⚠️ MCP servers still not working properly"
    echo "Check logs for more details:"
    echo "sudo journalctl -u mcp-client -f"
fi

# Cleanup
rm -f test_mcp_connection.py

print_success "Comprehensive MCP diagnostic and fix completed!"

echo -e "\n🎯 Quick test commands:"
echo "curl http://************:8000/servers"
echo "curl http://************:8000/tools"
echo 'curl -X POST http://************:8000/chat -H "Content-Type: application/json" -d '"'"'{"message": "Test MCP tools", "use_tools": true}'"'"
