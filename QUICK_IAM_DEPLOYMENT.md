# Quick IAM Role Deployment Guide

This is a streamlined guide for deploying your MCP Client with IAM roles on Amazon Linux.

## Prerequisites

- AWS Account with IAM permissions
- EC2 instance running Amazon Linux 2 or 2023

## Step 1: Create IAM Role (One-time setup)

### Quick AWS CLI Commands:

```bash
# 1. Create the policy
cat > mcp-client-policy.json <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": "arn:aws:bedrock:*:*:foundation-model/anthropic.claude-*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "pricing:GetProducts",
                "ce:GetCostAndUsage"
            ],
            "Resource": "*"
        }
    ]
}
EOF

aws iam create-policy \
    --policy-name MCP-Client-Policy \
    --policy-document file://mcp-client-policy.json

# 2. Create trust policy
cat > trust-policy.json <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {"Service": "ec2.amazonaws.com"},
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF

# 3. Create role
aws iam create-role \
    --role-name MCP-Client-EC2-Role \
    --assume-role-policy-document file://trust-policy.json

# 4. Attach policy to role
aws iam attach-role-policy \
    --role-name MCP-Client-EC2-Role \
    --policy-arn arn:aws:iam::$(aws sts get-caller-identity --query Account --output text):policy/MCP-Client-Policy

# 5. Create instance profile
aws iam create-instance-profile --instance-profile-name MCP-Client-EC2-Profile
aws iam add-role-to-instance-profile \
    --instance-profile-name MCP-Client-EC2-Profile \
    --role-name MCP-Client-EC2-Role
```

## Step 2: Launch EC2 with IAM Role

```bash
aws ec2 run-instances \
    --image-id ami-0c02fb55956c7d316 \
    --instance-type t3.small \
    --key-name your-key-pair \
    --security-group-ids sg-xxxxxxxxx \
    --iam-instance-profile Name=MCP-Client-EC2-Profile \
    --associate-public-ip-address \
    --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=MCP-Client}]'
```

**OR** attach to existing instance:

```bash
aws ec2 associate-iam-instance-profile \
    --instance-id i-xxxxxxxxx \
    --iam-instance-profile Name=MCP-Client-EC2-Profile
```

## Step 3: Deploy Application

### Connect to your instance:
```bash
ssh -i your-key.pem ec2-user@your-instance-ip
```

### Create your application files:
```bash
# Create main.py
nano main.py
# Copy your main.py content (with IAM role support)

# Create server_config.json
nano server_config.json
# Copy your server_config.json content

# Create requirements.txt
nano requirements.txt
# Copy your requirements.txt content
```

### Create and run deployment script:
```bash
# Create deployment script
nano amazon-linux-deploy.sh
# Copy the updated amazon-linux-deploy.sh content

# Make executable and run
chmod +x amazon-linux-deploy.sh
sudo ./amazon-linux-deploy.sh
```

## Step 4: Start the Service

```bash
# Start the service
sudo systemctl start mcp-client

# Check status
sudo systemctl status mcp-client

# View logs
sudo journalctl -u mcp-client -f
```

## Step 5: Test Your Application

```bash
# Get your public IP
PUBLIC_IP=$(curl -s http://***************/latest/meta-data/public-ipv4)
echo "Your application is at: http://$PUBLIC_IP:8000"

# Test the application
curl http://$PUBLIC_IP:8000/

# Test chat endpoint
curl -X POST http://$PUBLIC_IP:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello!", "use_tools": false}'
```

## Verification

### Check IAM role is working:
```bash
# Should show your role ARN
aws sts get-caller-identity

# Should list Bedrock models
aws bedrock list-foundation-models --region ap-south-1
```

### Check application logs:
```bash
sudo journalctl -u mcp-client -n 50
# Should see: "Bedrock client initialized successfully using IAM role"
```

## Security Group

Make sure your EC2 Security Group allows:
- **Port 22** (SSH) from your IP
- **Port 8000** (Application) from 0.0.0.0/0

## That's it!

Your MCP Client is now running securely with IAM roles at:
**http://YOUR_EC2_PUBLIC_IP:8000**

No credentials needed in files - everything is handled by AWS IAM! 🎉

## Troubleshooting

**"Unable to locate credentials":**
```bash
# Check IAM role is attached
curl http://***************/latest/meta-data/iam/security-credentials/
```

**"Access Denied":**
```bash
# Check policy is attached
aws iam list-attached-role-policies --role-name MCP-Client-EC2-Role
```

**Service won't start:**
```bash
# Check detailed logs
sudo journalctl -u mcp-client --no-pager
```
