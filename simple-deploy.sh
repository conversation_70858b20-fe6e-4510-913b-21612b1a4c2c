#!/bin/bash

# Simple MCP Client Deployment Script for AWS EC2 (No Nginx)
# Run this script as root or with sudo privileges

set -e  # Exit on any error

echo "Starting Simple MCP Client deployment..."

# Configuration
APP_NAME="mcp-client"
APP_DIR="/opt/${APP_NAME}"
LOG_DIR="/var/log/${APP_NAME}"
SERVICE_FILE="${APP_NAME}.service"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   log_error "This script must be run as root (use sudo)"
   exit 1
fi

# Update system packages
log_info "Updating system packages..."
apt update && apt upgrade -y

# Install required system packages
log_info "Installing system dependencies..."
apt install -y python3 python3-pip python3-venv ufw fail2ban htop curl wget git unzip

# Create application user
log_info "Creating application user: mcpuser"
if ! id "mcpuser" &>/dev/null; then
    useradd --system --shell /bin/bash --home-dir ${APP_DIR} --create-home mcpuser
else
    log_warn "User mcpuser already exists"
fi

# Create application directory
log_info "Setting up application directory: ${APP_DIR}"
mkdir -p ${APP_DIR}

# Create log directory
log_info "Creating log directory: ${LOG_DIR}"
mkdir -p ${LOG_DIR}

# Copy application files
log_info "Copying application files..."
cp main.py ${APP_DIR}/
cp server_config.json ${APP_DIR}/
cp requirements.txt ${APP_DIR}/
cp .env.production ${APP_DIR}/.env

# Setup Python virtual environment
log_info "Setting up Python virtual environment..."
sudo -u mcpuser python3 -m venv ${APP_DIR}/venv
sudo -u mcpuser ${APP_DIR}/venv/bin/pip install --upgrade pip

# Install Python dependencies
log_info "Installing Python dependencies..."
sudo -u mcpuser ${APP_DIR}/venv/bin/pip install -r ${APP_DIR}/requirements.txt

# Set proper permissions
log_info "Setting file permissions..."
chown -R mcpuser:mcpuser ${APP_DIR}
chown -R mcpuser:mcpuser ${LOG_DIR}
chmod -R 755 ${APP_DIR}
chmod 600 ${APP_DIR}/.env

# Install systemd service
log_info "Installing systemd service..."
cp ${SERVICE_FILE} /etc/systemd/system/
systemctl daemon-reload
systemctl enable ${SERVICE_FILE}

# Configure firewall
log_info "Configuring firewall..."
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 8000/tcp
ufw --force enable

# Configure fail2ban
log_info "Configuring fail2ban..."
systemctl enable fail2ban
systemctl start fail2ban

log_info "Simple deployment completed successfully!"
echo ""
log_warn "IMPORTANT: Please update the .env file with your actual AWS credentials:"
log_warn "  sudo nano ${APP_DIR}/.env"
echo ""
log_info "To start the service:"
log_info "  sudo systemctl start ${APP_NAME}"
echo ""
log_info "To check status:"
log_info "  sudo systemctl status ${APP_NAME}"
echo ""
log_info "To view logs:"
log_info "  sudo journalctl -u ${APP_NAME} -f"
echo ""
log_info "Your application will be accessible at:"
log_info "  http://YOUR_EC2_PUBLIC_IP:8000"
echo ""
log_warn "Remember to update your EC2 Security Group to allow inbound traffic on port 8000"
